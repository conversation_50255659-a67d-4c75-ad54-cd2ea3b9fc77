<!--
  价格显示组件
  功能：统一的价格展示组件，支持单价和价格区间
  特性：
  - 支持单个价格和价格区间显示
  - 多种尺寸和颜色主题
  - 自动格式化价格（分转元）
  - 整数和小数部分分别样式控制
  - 支持自定义区间标签
-->
<template>
  <!-- 价格区间显示 -->
  <span
    v-if="isRange"
    class="price-display price-range"
    :class="[sizeClass, colorClass, { 'price-bold': bold }]"
  >
    <span class="range-label" v-if="rangeLabel">{{ rangeLabel }}：</span>
    <span class="price-item">
      <span class="currency">¥</span>
      <span class="integer">{{ lowIntegerPart }}</span>
      <span class="decimal" v-if="lowDecimalPart">.{{ lowDecimalPart }}</span>
    </span>
    <span class="range-separator"> - </span>
    <span class="price-item">
      <span class="currency">¥</span>
      <span class="integer">{{ highIntegerPart }}</span>
      <span class="decimal" v-if="highDecimalPart">.{{ highDecimalPart }}</span>
    </span>
  </span>

  <!-- 单个价格显示 -->
  <span
    v-else
    class="price-display"
    :class="[sizeClass, colorClass, { 'price-bold': bold }]"
  >
    <span class="currency">¥</span>
    <span class="integer">{{ integerPart }}</span>
    <span class="decimal" v-if="decimalPart">.{{ decimalPart }}</span>
  </span>
</template>

<script setup>
import { computed } from 'vue'

// 组件属性定义
const props = defineProps({
  // 单个价格值
  price: {
    type: [Number, String],
    required: false
  },
  // 价格区间 - 高价
  highPrice: {
    type: [Number, String],
    required: false
  },
  // 价格区间 - 低价
  lowPrice: {
    type: [Number, String],
    required: false
  },
  // 价格区间标签
  rangeLabel: {
    type: String,
    default: ''
  },
  // 尺寸：small | medium | large
  size: {
    type: String,
    default: 'medium',
    validator: value => ['small', 'medium', 'large'].includes(value)
  },
  // 颜色主题：primary | orange | red | white
  color: {
    type: String,
    default: 'primary',
    validator: value => ['primary', 'orange', 'red', 'white'].includes(value)
  },
  // 是否加粗显示
  bold: {
    type: Boolean,
    default: true
  }
})

// 计算属性 - 判断是否为价格区间显示模式
const isRange = computed(() => {
  return (props.highPrice !== null && props.highPrice !== undefined) ||
    (props.lowPrice !== null && props.lowPrice !== undefined)
})

// 价格格式化工具函数（分转元，保留两位小数）
const formatPrice = (price) => {
  if (price === null || price === undefined) {
    return '--.-'
  }

  // 处理字符串格式价格（如 "¥100"）
  if (typeof price === 'string') {
    const numericPrice = parseFloat(price.replace(/[^\d.]/g, ''))
    return isNaN(numericPrice) ? '--.-' : (numericPrice / 100).toFixed(2)
  }

  // 数字格式价格（假设传入的是分为单位）
  const priceValue = price / 100
  return priceValue.toFixed(2)
}

// 计算属性 - 单个价格格式化
const priceFormatted = computed(() => formatPrice(props.price))

// 计算属性 - 单个价格的整数部分
const integerPart = computed(() => {
  if (props.price === null || props.price === undefined) {
    return '--'
  }
  return priceFormatted.value.split('.')[0]
})

// 计算属性 - 单个价格的小数部分
const decimalPart = computed(() => {
  if (props.price === null || props.price === undefined) {
    return '-'
  }
  return priceFormatted.value.split('.')[1]
})

// 计算属性 - 高价格格式化
const highPriceFormatted = computed(() => formatPrice(props.highPrice))

// 计算属性 - 高价格的整数部分
const highIntegerPart = computed(() => {
  if (props.highPrice === null || props.highPrice === undefined) {
    return '--'
  }
  return highPriceFormatted.value.split('.')[0]
})

// 计算属性 - 高价格的小数部分
const highDecimalPart = computed(() => {
  if (props.highPrice === null || props.highPrice === undefined) {
    return '-'
  }
  return highPriceFormatted.value.split('.')[1]
})

// 计算属性 - 低价格格式化
const lowPriceFormatted = computed(() => formatPrice(props.lowPrice))

// 计算属性 - 低价格的整数部分
const lowIntegerPart = computed(() => {
  if (props.lowPrice === null || props.lowPrice === undefined) {
    return '--'
  }
  return lowPriceFormatted.value.split('.')[0]
})

// 计算属性 - 低价格的小数部分
const lowDecimalPart = computed(() => {
  if (props.lowPrice === null || props.lowPrice === undefined) {
    return '-'
  }
  return lowPriceFormatted.value.split('.')[1]
})

// 计算属性 - 尺寸样式类
const sizeClass = computed(() => `price-${props.size}`)

// 计算属性 - 颜色样式类
const colorClass = computed(() => `price-${props.color}`)
</script>

<style scoped lang="less">
.price-display {
  font-family: 'D-DIN-PRO SemiBold';
  display: inline-flex;
  align-items: baseline;

  .currency {
    margin-right: 2px;
    font-weight: 400;
  }

  .integer {
    font-weight: 400;
  }

  .decimal {
    font-weight: 400;
  }

  // 尺寸变体
  &.price-small {
    .currency {
      font-size: 12px;
    }

    .integer {
      font-size: 16px;
    }

    .decimal {
      font-size: 12px;
    }
  }

  &.price-medium {
    .currency {
      font-size: 14px;
    }

    .integer {
      font-size: 20px;
    }

    .decimal {
      font-size: 14px;
    }
  }

  &.price-large {
    .currency {
      font-size: 16px;
    }

    .integer {
      font-size: 24px;
    }

    .decimal {
      font-size: 16px;
    }
  }

  // 颜色变体
  &.price-primary {
    color: #171E24;
  }

  &.price-orange {
    color: var(--wo-biz-theme-color);
  }

  &.price-red {
    color: var(--wo-biz-theme-color);
  }

  &.price-white {
    color: #FFFFFF;
  }

  // 加粗变体
  &.price-bold {
    .currency {
      font-weight: 600;
    }

    .integer {
      font-weight: 700;
    }

    .decimal {
      font-weight: 700;
    }
  }

  // 价格区间样式
  &.price-range {
    display: inline-flex;
    align-items: baseline;

    .range-label {
      //margin-right: 4px;
      font-size: 12px;
      font-weight: 400;
    }

    .price-item {
      display: inline-flex;
      align-items: baseline;
    }

    .range-separator {
      margin: 0 4px;
      font-size: inherit;
      font-weight: 400;
    }
  }
}
</style>
