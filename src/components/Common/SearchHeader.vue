<!--
  搜索头部组件
  功能：通用的搜索输入框组件
  特性：
  - 支持双向数据绑定
  - 防抖搜索优化性能
  - 支持跳转到搜索页面模式
  - 可自定义搜索图标和右侧操作区
  - 支持键盘回车搜索
  - 暴露输入框操作方法
-->
<template>
  <header class="search-header">
    <div class="search-header__input-wrapper" @click="handleInputClick">
      <!-- 搜索图标插槽 -->
      <slot name="search-icon">
        <img
          src="@/static/images/search.png"
          alt="搜索"
          class="search-header__icon"
          loading="lazy"
        />
      </slot>

      <!-- 搜索输入框 -->
      <input
        ref="inputRef"
        v-model="keyword"
        type="text"
        class="search-header__input"
        :placeholder="placeholder"
        :readonly="redirectToSearch"
        @keyup.enter="debouncedSearch"
      />

      <!-- 搜索按钮 -->
      <button
        type="button"
        class="search-header__button"
        @click.stop="debouncedSearch"
      >
        搜索
      </button>
    </div>

    <!-- 右侧操作区插槽 -->
    <slot name="right-action" />
  </header>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { debounce } from 'lodash-es'

// 路由实例
const router = useRouter()

// 组件属性定义
const props = defineProps({
  // 搜索关键词（支持 v-model）
  modelValue: {
    type: String,
    default: ''
  },
  // 输入框占位符文本
  placeholder: {
    type: String,
    default: '搜索'
  },
  // 是否点击时跳转到搜索页面
  redirectToSearch: {
    type: Boolean,
    default: false
  },
  // 跳转的搜索页面路径
  redirectUrl: {
    type: String,
    default: '/search'
  }
})

// 事件定义
const emit = defineEmits(['update:modelValue', 'search', 'clickable'])

// 响应式数据
const keyword = ref(props.modelValue)
const inputRef = ref(null)

// 监听器 - 外部 modelValue 变化时同步内部状态
watch(() => props.modelValue, (newVal) => {
  keyword.value = newVal
}, { immediate: true })

// 监听器 - 内部关键词变化时触发双向绑定更新
watch(keyword, (newVal) => {
  emit('update:modelValue', newVal)
})

// 防抖搜索处理函数（性能优化，避免频繁触发搜索）
const debouncedSearch = debounce(() => {
  const trimmedKeyword = keyword.value?.trim()
  if (trimmedKeyword) {
    emit('search', trimmedKeyword)
  }
}, 300)

// 输入框点击事件处理
const handleInputClick = () => {
  if (props.redirectToSearch) {
    // 跳转模式：导航到指定的搜索页面
    router.push(props.redirectUrl)
  } else {
    // 输入模式：聚焦输入框
    inputRef.value?.focus()
  }

  // 触发点击事件，供外部监听
  emit('clickable')
}

// 暴露给父组件的方法和属性
defineExpose({
  inputRef,
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur()
})
</script>

<style scoped lang="less">
.search-header {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #FFFFFF;
  box-sizing: border-box;

  &__input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    height: 32px;
    background-color: #FFFFFF;
    border-radius: 30px;
    padding: 3px 3px 3px 11px;
    box-sizing: border-box;
    border: 1px solid #E2E8EE;
    transition: border-color 0.2s ease;

    //&:focus-within {
    //  border-color: var(--wo-biz-theme-color);
    //}
  }

  &__icon {
    width: 13px;
    height: 13px;
    margin-right: 6px;
    flex-shrink: 0;
    image-rendering: -webkit-optimize-contrast;
  }

  &__input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 13px;
    color: #171E24;
    outline: none;
    min-width: 0;

    &::placeholder {
      color: #718096;
    }

    &::-webkit-input-placeholder {
      color: #718096;
    }

    &::-moz-placeholder {
      color: #718096;
    }
  }

  &__button {
    width: 50px;
    height: 26px;
    background-image: var(--wo-biz-theme-gradient-2);
    border-radius: 15px;
    font-size: 13px;
    font-weight: 500;
    text-align: center;
    color: #FFFFFF;
    border: none;
    cursor: pointer;
    flex-shrink: 0;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}
</style>
