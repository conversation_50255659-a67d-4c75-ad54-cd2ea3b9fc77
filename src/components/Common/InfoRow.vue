<!--
  信息行组件
  功能：展示标签-值对信息的通用组件
  特性：
  - 支持自定义值样式类
  - 可选择显示右侧箭头图标
  - 支持插槽自定义值内容
  - 响应式布局设计
-->
<template>
  <div class="row-section">
    <div class="label">{{ label }}</div>
    <div class="value" :class="valueClass">
      <slot name="value">
        <span v-if="!showArrow" class="value-text">{{ value }}</span>
        <template v-else>
          <span class="value-text">{{ value }}</span>
          <img
            :src="arrowIcon || arrowIconImg"
            :alt="arrowAlt"
            class="arrow-icon"
          />
        </template>
      </slot>
    </div>
  </div>
</template>

<script setup>
import arrowIconImg from '../../static/images/arrow-right-gray.png'

// 组件属性定义
defineProps({
  // 标签文本（必填）
  label: {
    type: String,
    required: true
  },
  // 值文本
  value: {
    type: String,
    default: ''
  },
  // 值区域的自定义样式类
  valueClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 是否显示右侧箭头图标
  showArrow: {
    type: Boolean,
    default: false
  },
  // 自定义箭头图标路径
  arrowIcon: {
    type: String,
    default: ''
  },
  // 箭头图标的无障碍描述
  arrowAlt: {
    type: String,
    default: '箭头'
  }
})
</script>

<style scoped lang="less">
.row-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    font-size: 13px;
    color: #4A5568;
    line-height: 1.5;
    min-width: 65px;
    flex-shrink: 0;
  }

  .value {
    font-size: 13px;
    color: #171E24;
    line-height: 1.5;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    margin-left: 12px;
    text-align: right;
    overflow: hidden;

    .value-text {
      overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
      max-width: 100%;
    }

    &.activity-text {
      color: var(--wo-biz-theme-color);
      font-weight: 500;
    }

    &.activity-no-text {
      color: #718096;
      font-weight: 400;
    }

    .arrow-icon {
      margin-left: 10px;
      width: 6px;
      height: 12px;
      color: #718096;
      font-size: 16px;
      vertical-align: middle;
      flex-shrink: 0;
    }
  }
}
</style>
