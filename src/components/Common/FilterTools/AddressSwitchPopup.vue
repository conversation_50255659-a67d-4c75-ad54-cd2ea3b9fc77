<template>
  <van-popup
    v-model:show="isVisible"
    closeable
    position="right"
    :close-on-click-overlay="true"
    :style="{ width: '80%',height:'100vh',overflow:'hidden' }"
    @open="popupOpen"
    @close="popupClose"
  >
    <div class="filter-area" ref="scrollContainer">
      <div class="filter-area-address-list">
        <div class="filter-area-address-item" @click="onAddrClick(item)" v-for="item in addressList" :key="item.addressId">
          <div class="filter-area-address-item-left">
            <img :src="addrSelectIcon" v-if="item.isDefault==='1'"/>
            <img src="./assets/address.png" alt="" srcset="" v-else>
          </div>
          <div class="filter-area-address-item-right">
            <div class="base-address">
              <span>{{ item.provinceName }}</span>
              <span>{{ item.cityName }}</span>
              <span>{{ item.countyName }}</span>
              <span>{{ item.townName }}</span>
            </div>
            <div class="address-detail" :class="{'address-detail-select':item.isDefault==='1'}">{{ item.addrDetail }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="filter-area-operator">
      <button class="btn-base btn3" @click="onAddClick">新建收货地址</button>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@store/modules/user.js'
import { updateUserDefaultAddr } from '@api/index.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { getBizCode } from '@utils/curEnv.js'
import woAddrSelect from './assets/wo-address-select.png'
import jdAddrSelect from './assets/jd-address-select.png'

const router = useRouter()
const userStore = useUserStore()

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:show', 'address-changed'])

// 内部状态
// 业务图片切换
const addrSelectIcon = computed(() => (getBizCode() === 'ygjd' ? jdAddrSelect : woAddrSelect))

// 内部状态
const isVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const addressList = ref([])
const scrollContainer = ref(null)

// 监听弹窗显示状态
watch(isVisible, async (newVal) => {
  if (newVal) {
    addressList.value = []
    showLoadingToast()
    try {
      await userStore.queryAddrList({ force: true })

      // 滚动到顶部
      if (scrollContainer.value) {
        scrollContainer.value.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }

      const storeAddressList = userStore.addressList || []
      const defaultItem = storeAddressList.find(item => item.isDefault === '1')

      // 将默认地址移到第一位
      if (defaultItem && storeAddressList[0] !== defaultItem) {
        const defaultIndex = storeAddressList.indexOf(defaultItem)
        storeAddressList.unshift(...storeAddressList.splice(defaultIndex, 1))
      }

      addressList.value = storeAddressList
    } catch (error) {
      console.error('获取地址列表失败:', error)
      showToast('获取地址列表失败')
    } finally {
      closeToast()
    }
  }
})

// 弹窗打开事件
const popupOpen = () => {
  console.log('地址切换弹窗打开')
}

// 弹窗关闭事件
const popupClose = () => {
  console.log('地址切换弹窗关闭')
}

// 地址点击处理方法
const onAddrClick = async (item) => {
  if (item.isDefault === '1') {
    return
  }

  // 更新本地状态
  addressList.value.forEach(function (addr) {
    addr.isDefault = '0'
  })
  item.isDefault = '1'

  try {
    const [err] = await updateUserDefaultAddr(item.addressId)
    if (err) {
      // 恢复原状态
      item.isDefault = '0'
      if (addressList.value && addressList.value.length > 0) {
        addressList.value[0].isDefault = '1'
      }
      showToast(err.msg)
      return
    }

    // 刷新默认地址数据
    await userStore.queryDefaultAddr({ force: true })
    showToast('更新地址成功')
    // 通知父组件地址已更改
    emit('address-changed')

    isVisible.value = false
  } catch (error) {
    // 恢复原状态
    item.isDefault = '0'
    if (addressList.value && addressList.value.length > 0) {
      addressList.value[0].isDefault = '1'
    }
    console.error('更新默认地址失败:', error)
    showToast('更新默认地址失败')
  }
}

// 新建地址点击处理
const onAddClick = () => {
  router.push('/address/add')
}
</script>

<style scoped lang="less">
.filter-area {
  position: relative;
  height: 80vh;
  margin-top: 50px;
  padding: 0 20px 0 20px;
  overflow: scroll;

  .filter-area-address-list {
    width: 100%;

    .filter-area-address-item {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 10px;

      .filter-area-address-item-left {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;

        img {
          width: 15px;
          height: 15px;
          vertical-align: middle;
        }
      }

      .filter-area-address-item-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;

        .base-address {
          margin-bottom: 3px;
          font-size: 13px;
          line-height: 1.5;
          color: #718096;
        }

        .address-detail {
          font-size: 15px;
          color: #171E24;
          line-height: 1.5;
          display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
        }

        .address-detail-select {
          font-weight: 700;
        }
      }
    }
  }
}

.filter-area-operator {
  padding: 0 20px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  height: 65px;
  background: #FFFFFF;
  box-sizing: border-box;

  .btn-base {
    height: 40px;
    width: 103px;
    font-size: 18px;
    color: var(--wo-biz-theme-color);
    text-align: center;
    line-height: 18px;
    font-weight: 400;
    border: none;
  }

  .btn3 {
    flex: 1 !important;
    color: #FFFFFF;
    background-image: var(--wo-biz-theme-gradient-1);
    border-radius: 50px;
  }
}
</style>
