<template>
  <van-popup
    class="popup common-popup"
    :style="{ minHeight: '240px' }"
    safe-area-inset-bottom
    lock-scroll
    round
    position="bottom"
    v-model:show="visible"
  >
    <div class="popup-header">
      <p class="title">{{ title }}</p>
      <img @click="handleClose" class="close" src="./assets/popupClose.png" alt="" srcset="">
    </div>
    <div class="popup-content">
      <div class="after-sales-expiration-content">
        <p class="after-sales-expiration-tips">{{ mainText }}</p>
        <p class="after-sales-expiration-sub-tips">{{ subText }}</p>
      </div>
    </div>
    <div class="popup-op" v-if="showConfirmButton">
      <div class="popup-op-btn" @click="handleConfirm">
        {{ confirmText }}
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { defineProps, defineEmits, toRefs } from 'vue'
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  mainText: {
    type: String,
    default: '抱歉，订单已过售后申请时效'
  },
  subText: {
    type: String,
    default: '商品已超过售后期限，如需售后可联系客服处理'
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  showConfirmButton: {
    type: Boolean,
    default: true
  }
})
const {
  visible,
  title,
  mainText,
  subText,
  confirmText,
  showConfirmButton
} = toRefs(props)
const emit = defineEmits(['update:visible', 'close', 'confirm'])

const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

const handleConfirm = () => {
  emit('update:visible', false)
  emit('confirm')
}
</script>

<style scoped lang="less">
.popup {
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .popup-header {
    display: flex;
    align-items: center;
    margin-bottom: 14px;

    .title {
      flex: 1;
      font-size: 17px;
      color: #171E24;
      text-align: center;
      line-height: 1.5;
      font-weight: 400;
    }

    .close {
      width: 14px;
      height: 14px;
    }
  }

  .popup-content {
    margin-bottom: 50px;

    .after-sales-expiration-content {
      .after-sales-expiration-tips {
        font-size: 19px;
        color: #171E24;
        text-align: center;
        font-weight: 700;
        margin-bottom: 15px;
      }

      .after-sales-expiration-sub-tips {
        margin-top: 10px;
        font-size: 13px;
        color: #4A5568;
        text-align: center;
        font-weight: 400;
      }
    }
  }

  .popup-op {
    width: 100%;
    height: 35px;
    margin-top: 20px;

    .popup-op-btn {
      background-image: var(--wo-biz-theme-gradient-1);
      border-radius: 49px;
      font-size: 17px;
      color: #FFFFFF;
      font-weight: 400;
      width: 100%;
      height: 35px;
      text-align: center;
      line-height: 35px;
      cursor: pointer;

      &:active {
        background-image: var(--wo-biz-theme-gradient-3);
      }
    }
  }
}
</style>
