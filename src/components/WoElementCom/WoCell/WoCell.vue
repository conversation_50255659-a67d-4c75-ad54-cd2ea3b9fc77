<template>
  <div class="wo-cell" :class="{ 'is-center': isCenter, 'is-border': isBorder }">
    <div class="cell-left" :class="{ 'is-require': isRequire }">
      <div class="left-title">{{ leftTitle }}</div>
    </div>
    <div class="cell-right" :class="{ 'is-vertical': isVertical }" @click="handleRightClick">
      <slot name="right">
        <div class="right-title">{{ rightTitle }}</div>
      </slot>
      <img v-if="showArrow" class="right-arrow" :src="arrowSrc || defaultArrowSrc" alt="" srcset="">
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, toRefs } from 'vue'
import defaultArrowImg from '@/static/images/arrow-right-gray.png' // 导入默认箭头图片

const defaultArrowSrc = defaultArrowImg // 设置默认箭头图片路径

const props = defineProps({
  leftTitle: {
    type: String,
    default: ''
  },
  rightTitle: {
    type: String,
    default: ''
  },
  isCenter: {
    type: Boolean,
    default: true
  },
  isBorder: {
    type: Boolean,
    default: false
  },
  isRequire: {
    type: Boolean,
    default: false
  },
  isVertical: {
    type: Boolean,
    default: false
  },
  showArrow: {
    type: Boolean,
    default: false
  },
  arrowSrc: {
    type: String,
    default: ''
  }
})

const { leftTitle, rightTitle, isCenter, isBorder, isRequire, isVertical, showArrow, arrowSrc } = toRefs(props)


const emit = defineEmits(['rightClick'])

const handleRightClick = () => {
  emit('rightClick')
}
</script>


<style scoped lang="less">
.wo-cell {
  width: 100%;
  min-height: 55px;
  display: flex;
  justify-content: space-between;
  padding: 15px 0;
  box-sizing: border-box;
  &.is-center {
    align-items: center;
  }

  &.is-border {
    border-bottom: 1px solid #E2E8EE;
  }

  .is-require {
    position: relative;

    &:after {
      content: '*';
      position: absolute;
      top: -3px;
      right: -10px;
      color: #EF4444;
      font-size: 25px;
    }
  }

  .is-vertical {
    flex-direction: column;
  }

  .cell-left {
    min-width: 65px;
    margin-right: 10px;

    .left-title {
      font-size: 16px;
      color: #171E24;
      line-height: 1;
      font-weight: 400;
    }
  }

  .cell-right {
    display: flex;
    align-items: center;

    .right-title {
      font-size: 15px;
      color: #171E24;
      line-height: 1;
      font-weight: 400;
    }

    .right-arrow {
      margin-left: 5px;
      width: 7px;
      height: 12px;
    }
  }
}
</style>
