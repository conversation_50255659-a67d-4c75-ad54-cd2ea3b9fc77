<template>
  <div>
    <Dialog v-model:show="isShow" :title="title" :show-confirm-button="showConfirmButton"
      :show-cancel-button="showCancelButton" :confirm-button-text="confirmButtonText"
      :cancel-button-text="cancelButtonText" :confirm-button-color="confirmButtonColor"
      :cancel-button-color="cancelButtonColor" :close-on-click-overlay="closeOnClickOverlay" :width="width"
      :class-name="className" :theme="theme" :before-close="beforeClose" :transition="transition"
      :get-container="getContainer" @confirm="onConfirm" @cancel="onCancel">
      <div v-if="message" class="alert-message" :style="messageStyle" :class="messageAlignClass">{{ message }}</div>
      <div v-if="messageHtml" class="alert-message" v-html="messageHtml" :style="messageStyle"
        :class="messageAlignClass"></div>
      <slot></slot>
    </Dialog>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Dialog } from 'vant'

// 定义组件名称
defineOptions({
  name: 'WoAlert'
})

// 定义props
const props = defineProps({
  // 弹窗标题
  title: {
    type: String,
    default: ''
  },
  // 弹窗内容
  message: {
    type: String,
    default: ''
  },
  // 弹窗HTML内容
  messageHtml: {
    type: String,
    default: ''
  },
  // 内容样式
  messageStyle: {
    type: Object,
    default: () => ({})
  },
  // 内容对齐方式，可选值为left right，默认center
  messageAlign: {
    type: String,
    default: 'center',
    validator: value => ['left', 'center', 'right'].includes(value)
  },
  // 是否显示确认按钮
  showConfirmButton: {
    type: Boolean,
    default: true
  },
  // 是否显示取消按钮
  showCancelButton: {
    type: Boolean,
    default: false
  },
  // 确认按钮文字
  confirmButtonText: {
    type: String,
    default: '确定'
  },
  // 取消按钮文字
  cancelButtonText: {
    type: String,
    default: '取消'
  },
  // 确认按钮颜色
  confirmButtonColor: {
    type: String,
    default: 'var(--wo-biz-theme-color)'
  },
  // 取消按钮颜色
  cancelButtonColor: {
    type: String,
    default: '#171E24'
  },
  // 是否在点击遮罩层后关闭弹窗
  closeOnClickOverlay: {
    type: Boolean,
    default: false
  },
  // 弹窗宽度，默认320px
  width: {
    type: [String, Number],
    default: '300px'
  },
  // 样式风格，可选值为round，默认default
  theme: {
    type: String,
    default: 'default',
    validator: value => ['default', 'round'].includes(value)
  },
  // 自定义类名
  className: {
    type: String,
    default: ''
  },
  // 关闭前的回调函数，调用done()后关闭弹窗，调用done(false)阻止弹窗关闭
  beforeClose: {
    type: Function,
    default: null
  },
  // 动画类名，等价于transition的name属性
  transition: {
    type: String,
    default: ''
  },
  // 指定挂载的节点
  getContainer: {
    type: [String, Object, Function],
    default: null
  },
  // 确认回调
  onConfirmCallback: {
    type: Function,
    default: null
  },
  // 取消回调
  onCancelCallback: {
    type: Function,
    default: null
  }
})

// 定义事件
const emit = defineEmits(['confirm', 'cancel'])

// 响应式数据
const isShow = ref(false)

// 计算属性
const messageAlignClass = computed(() => {
  if (props.messageAlign === 'left') return 'text-left'
  if (props.messageAlign === 'right') return 'text-right'
  return 'text-center'
})

// 方法
// 显示弹窗
function show() {
  isShow.value = true
  return { show, close, onConfirm, onCancel }
}

// 关闭弹窗
function close() {
  isShow.value = false
  return { show, close, onConfirm, onCancel }
}

// 确认按钮点击事件
function onConfirm() {
  if (props.onConfirmCallback) {
    props.onConfirmCallback()
  }
  emit('confirm')
}

// 取消按钮点击事件
function onCancel() {
  if (props.onCancelCallback) {
    props.onCancelCallback()
  }
  emit('cancel')
}

// 暴露方法给父组件
defineExpose({
  show,
  close,
  onConfirm,
  onCancel
})
</script>

<style scoped lang="less">
.alert-message {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 110px;
  padding: 20px;
  line-height: 1.5;
  font-size: 16px;
  color: #171E24;
  white-space: pre-wrap;
  box-sizing: border-box;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}
</style>
