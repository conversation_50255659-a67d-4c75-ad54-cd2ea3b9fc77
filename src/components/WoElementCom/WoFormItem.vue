<template>
  <div class="wo-form-item" :class="{'wo-form-item-arrow': arrow}">
    <van-field
      v-model="fieldValue"
      :label="label"
      :placeholder="placeholder"
      :readonly="readonly"
      :disabled="disabled"
      :required="required"
      :error="error"
      :error-message="errorMessage"
      :label-width="labelWidth"
      :label-align="labelAlign"
      :input-align="inputAlign"
      :type="type"
      :maxlength="maxlength"
      :rows="rows"
      :autosize="autosize"
      :clearable="clearable"
      :clickable="clickable"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @click="handleClick"
      @clear="handleClear"
    >
      <!-- 自定义左侧图标 -->
      <template #left-icon v-if="$slots.leftIcon">
        <slot name="leftIcon"></slot>
      </template>

      <!-- 自定义右侧图标 -->
      <template #right-icon v-if="$slots.rightIcon || arrow">
        <slot name="rightIcon">
          <img v-if="arrow" src="../../static/images/arrow-right-black.png" alt="箭头" class="arrow-icon">
        </slot>
      </template>

      <!-- 自定义输入框内容 -->
      <template #input v-if="$slots.input">
        <slot name="input"></slot>
      </template>

      <!-- 自定义标签内容 -->
      <template #label v-if="$slots.label">
        <slot name="label"></slot>
      </template>

      <!-- 自定义按钮 -->
      <template #button v-if="$slots.button">
        <slot name="button"></slot>
      </template>

      <!-- 自定义额外内容 -->
      <template #extra v-if="$slots.extra">
        <slot name="extra"></slot>
      </template>
    </van-field>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  // 原有属性
  label: {
    type: String,
    required: true
  },
  arrow: {
    type: Boolean,
    default: false
  },
  // Vant Field 相关属性
  modelValue: {
    type: [String, Number],
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入'
  },
  readonly: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  error: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  labelWidth: {
    type: [String, Number],
    default: '6.2em'
  },
  labelAlign: {
    type: String,
    default: 'left'
  },
  inputAlign: {
    type: String,
    default: 'left'
  },
  type: {
    type: String,
    default: 'text'
  },
  maxlength: {
    type: [String, Number],
    default: undefined
  },
  rows: {
    type: [String, Number],
    default: 1
  },
  autosize: {
    type: [Boolean, Object],
    default: false
  },
  clearable: {
    type: Boolean,
    default: false
  },
  clickable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'update:modelValue',
  'input',
  'focus',
  'blur',
  'click',
  'clear'
])

const fieldValue = ref(props.modelValue)

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  fieldValue.value = newVal
})

// 监听内部值变化
watch(fieldValue, (newVal) => {
  emit('update:modelValue', newVal)
})

// 事件处理
const handleInput = (value) => {
  emit('input', value)
}

const handleFocus = (event) => {
  emit('focus', event)
}

const handleBlur = (event) => {
  emit('blur', event)
}

const handleClick = (event) => {
  emit('click', event)
}

const handleClear = (event) => {
  emit('clear', event)
}
</script>

<style scoped lang="less">
.wo-form-item {
  position: relative;

  :deep(.van-field) {
    padding: 18px 0;
    border-bottom: 1px solid #F0F2F5;
    background: transparent;
    transition: all 0.3s ease;
    position: relative;

    // 聚焦状态增强
    &:focus-within {
      border-bottom-color: var(--wo-biz-theme-color);

      .van-field__label {
        color: var(--wo-biz-theme-color);
        transform: translateY(-1px);
      }
    }

    .van-field__label {
      font-size: 15px;
      color: #171E24;
      font-weight: 500;
      min-width: 70px;
      margin-right: 12px;
      width: auto;
      transition: all 0.3s ease;
      letter-spacing: 0.2px;

      &::before {
        content: '';
        position: absolute;
        left: -4px;
        top: 50%;
        transform: translateY(-50%);
        width: 2px;
        height: 0;
        background: var(--wo-biz-theme-color);
        transition: height 0.3s ease;
      }
    }

    .van-field__control {
      font-size: 15px;
      color: #171E24;
      transition: all 0.3s ease;

      &::placeholder {
        color: #CBD5E0;
        opacity: 0.10;
        font-size: 14px;
        transition: color 0.3s ease;
      }

      &:focus {
        outline: none;
        color: #171E24;

        &::placeholder {
          color: #718096;
        }
      }
    }

    .van-field__body {
      align-items: center;
      position: relative;
    }

    // 移除默认边框
    &::after {
      display: none;
    }

    // 输入框聚焦时的标签动画
    &:focus-within .van-field__label::before {
      height: 12px;
    }

    // 错误状态样式
    &.van-field--error {
      border-bottom-color: #EF4444;

      .van-field__label {
        color: #EF4444;
      }

      .van-field__control {
        color: #EF4444;
      }
    }

    // 禁用状态样式
    &.van-field--disabled {
      background: #FAFBFC;
      border-bottom-color: #E2E8EE;

      .van-field__label,
      .van-field__control {
        color: #CBD5E0;
      }
    }
  }

  &-arrow {
    :deep(.van-field) {
      .van-field__body {
        display: flex;
        align-items: center;
        cursor: pointer;

        &:hover {
          .van-field__control {
            color: var(--wo-biz-theme-color);
          }
        }
      }
    }
  }
}

.arrow-icon {
  width: 8px;
  height: 12px;
  margin-left: 8px;
  opacity: 0.6;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
}
</style>
