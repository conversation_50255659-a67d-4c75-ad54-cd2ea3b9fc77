<!--
  底部操作栏组件
  功能：固定在页面底部的操作栏容器
  特性：
  - 固定在页面底部显示
  - 可配置距离底部的距离
  - 支持插槽自定义内容
  - 默认提供新增地址按钮
  - 统一的操作栏样式
-->
<template>
  <div class="action-bar">
    <slot>
      <!-- 默认插槽内容 -->
      <WoButton
        type="primary"
        block
        :size="size"
        @click="handleAddClick"
      >
        新增收货地址
      </WoButton>
    </slot>
  </div>
</template>

<script setup>
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

// 组件属性定义
const props = defineProps({
  // 距离底部的距离（px）
  bottom: {
    type: [Number, String],
    default: 0
  },
  // 按钮尺寸
  size: {
    type: String,
    default: 'default'
  }
})

// 事件定义
const emit = defineEmits(['add'])

// 默认新增按钮点击处理
const handleAddClick = () => {
  emit('add')
}
</script>

<style scoped lang="less">
.action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: v-bind('props.bottom + "px"');
  padding: 10px 10px;
  background-color: #FFFFFF;
  //box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 10;
  min-height: 55px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}
</style>
