<template>
  <div
    class="wo-button"
    :class="[
      `wo-button-${type}`,
      { 'wo-button-block': block },
      { 'wo-button-disabled': disabled },
      `wo-button-size-${size}`,
      { 'wo-button-round': round }
    ]"
    @click.stop="handleClick"
  >
    <slot></slot>
  </div>
</template>

<script setup>
const props = defineProps({
  type: {
    type: String,
    default: 'default', // primary, secondary, tertiary, danger, text, gradient, cancel
    validator: (value) => ['primary', 'secondary', 'tertiary', 'danger', 'text', 'gradient', 'cancel', 'default'].includes(value)
  },
  size: {
    type: String,
    default: 'large', // xlarge, large, medium, small, mini
    validator: (value) => ['xlarge', 'large', 'medium', 'small'].includes(value)
  },
  block: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  round: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const handleClick = (event) => {
  if (!props.disabled) {
    emit('click', event)
  }
}
</script>

<style scoped lang="less">
.wo-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  text-align: center;
  border: none;
  cursor: pointer;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;

  // 类型样式.
  &-default {
    background-color: #FFFFFF;
    color: #171E24;
    border: 1px solid rgba(198,201,204,1);

  }

  &-primary {
    background-color: var(--wo-biz-theme-color);
    color: #FFFFFF;

    &:active {
      background-color: var(--wo-biz-theme-active);
    }
  }

  &-secondary {
    background-color: #FFFFFF;
    color: var(--wo-biz-theme-color);
    border: 1px solid var(--wo-biz-theme-color);

    &:active {
      background-color: var(--wo-biz-theme-bg-1);
    }
  }

  &-tertiary {
    background-color: var(--wo-biz-theme-bg-1);
    color: var(--wo-biz-theme-color);

    &:active {
      background-color: var(--wo-biz-theme-bg-2);
    }
  }

  &-danger {
    background-color: #EF4444;
    color: #FFFFFF;

    &:active {
      background-color: #DC2626;
    }
  }

  &-text {
    background-color: transparent;
    color: var(--wo-biz-theme-color);
    padding: 0;

    &:active {
      opacity: 0.7;
    }
  }

  &-gradient {
    background-image: var(--wo-biz-theme-gradient-1);
    color: #FFFFFF;

    &:active {
      background-image: var(--wo-biz-theme-gradient-3);
    }
  }

  &-cancel {
    background-color: #F8F9FA;
    color: #718096;

    &:active {
      background-color: #E2E8F0;
    }
  }

  // 尺寸样式
  &-size-xlarge {
    height: 42px;
    font-size: 16px;
    width: 100%;
    border-radius: 22px;
  }

  &-size-large {
    height: 36px;
    font-size: 15px;
    width: 160px;
    border-radius: 22px;
  }

  &-size-medium {
    height: 36px;
    font-size: 15px;
    width: 90px;
    border-radius: 18px;
  }

  &-size-small {
    height: 28px;
    font-size: 12px;
    width: 80px;
    border-radius: 15px;
  }

  &-size-special {
    height: 38px;
    font-size: 15px;
    width: 119px;
    border-radius: 20px;
  }

  // 块级按钮
  &-block {
    width: 100%;
  }

  // 圆角按钮
  &-round {
    border-radius: 50px;
  }

  // 禁用状态
  &-disabled {
    opacity: 0.5;
    cursor: not-allowed;

    //&:active {
    //  background-color: inherit;
    //  background-image: inherit;
    //}
  }
}
</style>
