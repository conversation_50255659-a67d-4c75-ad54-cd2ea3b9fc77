<!--
  卡片容器组件
  功能：通用的卡片容器组件
  特性：
  - 可选的卡片标题显示
  - 统一的卡片样式和间距
  - 支持插槽自定义内容
  - 圆角边框和阴影效果
  - 响应式布局设计
-->
<template>
  <div class="card-section">
    <!-- 卡片标题（可选） -->
    <div v-if="title" class="card-title">
      {{ title }}
    </div>

    <!-- 卡片内容区域 -->
    <div class="card-content">
      <slot />
    </div>
  </div>
</template>

<script setup>
// 组件属性定义
defineProps({
  // 卡片标题文本
  title: {
    type: String,
    default: ''
  }
})
</script>

<style scoped lang="less">
.card-section {
  background-color: #FFFFFF;
  padding: 13px 15px;
  margin-bottom: 10px;
  border-radius: 10px;
}

.card-title {
  font-size: 15px;
  font-weight: 600;
  color: #171E24;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #E2E8EE;
}

.card-content {
  width: 100%;
}
</style>
