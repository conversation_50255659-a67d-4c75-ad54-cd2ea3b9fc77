<!--
  空状态组件
  功能：显示空数据状态的通用组件
  特性：
  - 基于 Vant Empty 组件封装
  - 支持自定义图片和描述文字
  - 可配置图片大小
  - 支持插槽自定义操作按钮
  - 统一的空状态样式
-->
<template>
  <div class="wo-empty">
    <van-empty
      :image="image"
      :image-size="imageSize"
      :description="description"
    >
      <template #default>
        <div class="wo-empty-action">
          <slot></slot>
        </div>
      </template>
    </van-empty>
  </div>
</template>

<script setup>
// 组件属性定义
defineProps({
  // 图片类型，可选值为 error network search，支持传入图片 URL
  image: {
    type: String,
    default: 'default'
  },
  // 图片大小，单位为 px
  imageSize: {
    type: [Number, String],
    default: 120
  },
  // 图片下方的描述文字
  description: {
    type: String,
    default: '暂无数据'
  }
})
</script>

<style scoped lang="less">
.wo-empty {
  padding: 60px 20px;

  &-action {
    margin-top: 24px;
  }
}
</style>
