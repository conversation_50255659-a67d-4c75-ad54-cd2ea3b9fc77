<template>
  <section class="introduce-section" ref="introduceSectionRef">
    <!-- 懒加载占位符 -->
    <div v-if="!isIntroduceVisible" class="introduce-placeholder">
      <div class="introduce-placeholder__title">商品信息</div>
      <div class="introduce-placeholder__content">
        <div class="introduce-placeholder__skeleton"></div>
      </div>
    </div>
    <!-- 实际组件 -->
    <GoodsIntroduce v-else :currentSKU="currentSKUForIntroduce" />
  </section>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import GoodsIntroduce from './GoodsIntroduce.vue'

const props = defineProps({
  currentSku: {
    type: Object,
    default: () => ({})
  },
  productIntroductionData: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['load-introduce-data'])

const introduceSectionRef = ref(null)
const isIntroduceVisible = ref(false)
const hasIntroduceDataLoaded = ref(false)
const hasUserScrolled = ref(false)

// 懒加载的商品介绍数据
const currentSKUForIntroduce = computed(() => {
  if (!isIntroduceVisible.value) {
    return {}
  }

  // 返回基础SKU数据，即使介绍数据还在加载中
  return {
    ...props.currentSku,
    introduction: hasIntroduceDataLoaded.value ? props.productIntroductionData : ''
  }
})

// Intersection Observer 实例
let introduceObserver = null

// 监听用户滚动行为
const handleUserScroll = () => {
  if (!hasUserScrolled.value) {
    hasUserScrolled.value = true
    console.log('检测到用户滚动行为')

    // 如果商品介绍还没有加载，重新检查是否应该加载
    if (!isIntroduceVisible.value && introduceSectionRef.value) {
      const rect = introduceSectionRef.value.getBoundingClientRect()
      const viewportHeight = window.innerHeight

      // 如果区域已经进入可视区域，立即加载
      if (rect.top < viewportHeight + 50 && rect.bottom > -50) {
        console.log('用户滚动后检测到商品介绍区域在可视区域内，立即加载')
        isIntroduceVisible.value = true
        loadIntroduceData()

        // 停止观察
        if (introduceObserver) {
          introduceObserver.unobserve(introduceSectionRef.value)
        }
      }
    }
  }
}

// 初始化懒加载观察器
const initIntroduceObserver = () => {
  if (!introduceSectionRef.value) return

  introduceObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        console.log('观察器触发:', {
          isIntersecting: entry.isIntersecting,
          isIntroduceVisible: isIntroduceVisible.value,
          hasUserScrolled: hasUserScrolled.value,
          isInInitialViewport: isInInitialViewport(entry.target)
        })

        if (entry.isIntersecting && !isIntroduceVisible.value) {
          // 恢复原来的逻辑：只有在用户滚动过或者区域不在初始可视区域时才加载
          if (hasUserScrolled.value || !isInInitialViewport(entry.target)) {
            console.log('商品介绍区域进入可视区域，开始加载')
            isIntroduceVisible.value = true

            // 开始加载数据
            loadIntroduceData()

            // 停止观察，避免重复触发
            introduceObserver.unobserve(entry.target)
          } else {
            console.log('区域在初始视口内且用户未滚动，暂不加载')
          }
        }
      })
    },
    {
      // 提前 50px 开始加载，但不要太早
      rootMargin: '50px 0px',
      threshold: 0.1
    }
  )

  introduceObserver.observe(introduceSectionRef.value)
}

// 检查元素是否在初始视口内
const isInInitialViewport = (element) => {
  const rect = element.getBoundingClientRect()
  const viewportHeight = window.innerHeight || document.documentElement.clientHeight

  // 修复判断逻辑：如果元素顶部在视口内且底部也可能在视口内，认为是初始可见的
  return rect.top < viewportHeight && rect.bottom > 0
}

// 加载商品介绍数据
const loadIntroduceData = async () => {
  if (!props.currentSku) {
    hasIntroduceDataLoaded.value = true
    return
  }

  try {
    // 如果是京东商品，需要请求接口获取数据
    if (props.currentSku.supplierCode && props.currentSku.supplierCode.indexOf('jd_') > -1) {
      console.log('开始请求京东商品介绍数据')
      emit('load-introduce-data')
    }

    hasIntroduceDataLoaded.value = true
    console.log('商品介绍数据加载完成')
  } catch (error) {
    console.error('加载商品介绍数据失败:', error)
    hasIntroduceDataLoaded.value = true
  }
}

// 初始化方法
const init = () => {
  // 延迟初始化商品介绍懒加载观察器，确保页面稳定后再开始监听
  setTimeout(() => {
    initIntroduceObserver()
    // 开始监听用户滚动
    window.addEventListener('scroll', handleUserScroll, { passive: true })
  }, 500)
}

onMounted(() => {
  init()
})

onUnmounted(() => {
  // 清理滚动事件监听器
  window.removeEventListener('scroll', handleUserScroll)

  // 清理 Intersection Observer
  if (introduceObserver) {
    introduceObserver.disconnect()
    introduceObserver = null
  }
})

// 暴露初始化方法给父组件
defineExpose({
  init
})
</script>

<style scoped lang="less">
// 商品介绍区域 - 标准padding
.introduce-section {
  background-color: #FFFFFF;
  padding: 10px 0;
  box-sizing: border-box;
}

.introduce-placeholder {
  padding: 20px;
  background-color: #FFFFFF;

  &__title {
    font-size: 16px;
    font-weight: 500;
    color: #171E24;
    margin-bottom: 16px;
  }

  &__content {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__skeleton {
    height: 200px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 8px;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}
</style>