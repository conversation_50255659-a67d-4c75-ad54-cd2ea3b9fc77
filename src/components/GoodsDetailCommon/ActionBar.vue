<!--
  商品详情操作栏组件
  功能：商品详情页面底部的操作栏
  特性：
  - 购物车图标和数量显示
  - 加入购物车和立即购买按钮
  - 根据业务类型显示不同的按钮文案
  - 支持按钮禁用状态
  - 固定在页面底部
-->
<template>
  <WoActionBar>
    <div class="action-bar">
      <!-- 操作区域 -->
      <div class="action-content">
        <!-- 购物车区域 -->
        <div class="cart-wrapper">
          <div class="cart-icon-wrapper" @click="handleGoToCart">
            <van-badge
              :content="cartCount"
              :show-zero="false"
              :max="99"
              class="cart-badge"
            >
              <img src="./assets/cart-icon.png" alt="购物车" class="cart-icon" />
            </van-badge>
          </div>
          <span class="cart-text">购物车</span>
        </div>

        <!-- 按钮组区域 -->
        <div class="buttons-wrapper">
          <WoButton
            type="secondary"
            size="medium"
            :disabled="cartButtonDisabled"
            @click="handleAddToCart"
          >
            加入购物车
          </WoButton>
          <WoButton
            type="gradient"
            size="medium"
            :disabled="cartButtonDisabled"
            @click="handleBuyNow"
          >
            {{ buyNowButtonText }}
          </WoButton>
        </div>
      </div>
    </div>
  </WoActionBar>
</template>

<script setup>
import { computed } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import { getBizCode } from '@utils/curEnv.js'

// 组件属性定义
defineProps({
  // 购物车商品数量
  cartCount: {
    type: Number,
    default: 0
  },
  // 购物车按钮是否禁用
  cartButtonDisabled: {
    type: Boolean,
    default: false
  }
})

// 事件定义
const emit = defineEmits(['go-to-cart', 'add-to-cart', 'buy-now'])

// 计算属性 - 根据业务类型显示不同的购买按钮文案
const buyNowButtonText = computed(() => {
  const bizCode = getBizCode()
  return bizCode === 'zq' ? '立即提交' : '立即购买'
})

// 跳转购物车处理
const handleGoToCart = () => {
  emit('go-to-cart')
}

// 加入购物车处理
const handleAddToCart = () => {
  emit('add-to-cart')
}

// 立即购买处理
const handleBuyNow = () => {
  emit('buy-now')
}
</script>

<style scoped lang="less">
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 0 17px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  border-top: 1px solid rgba(0, 0, 0, 0.05);

  // 操作区域
  .action-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .cart-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0 0 0;
    box-sizing: border-box;

    .cart-icon-wrapper {
      position: relative;

      .cart-icon {
        width: 24px;
        height: 24px;
      }

      .cart-badge {
        :deep(.van-badge) {
          background-color: var(--wo-biz-theme-color);
          border-color: var(--wo-biz-theme-color);
        }
      }

      // 购物车数量显示
      .num {
        position: absolute;
        top: -8px;
        right: -2px;
        min-width: 16px;
        height: 16px;
        background-color: var(--wo-biz-theme-color);
        color: white;
        border-radius: 8px;
        font-size: 11px;
        line-height: 16px;
        text-align: center;
        padding: 0 4px;
        box-sizing: border-box;
        z-index: 10;

        &.animation {
          animation: shake 0.6s ease-in-out;
        }
      }
    }

    .cart-text {
      font-size: 11px;
      color: #4A5568;
    }
  }

  .buttons-wrapper {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}

// 购物车震动动画
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}
</style>
