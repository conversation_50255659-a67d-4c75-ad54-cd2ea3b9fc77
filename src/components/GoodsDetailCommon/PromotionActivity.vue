<template>
  <section class="promotion-section" v-if="promotionList && promotionList.length > 0 && bizCode === 'ziying'">
    <div class="promotion-activity">
      <div class="promotion-activity-title">
        优惠活动
      </div>
      <div class="promotion-activity-content">
        <div
          class="promotion-item"
          v-for="(item, index) in promotionList"
          :key="index"
          @click="handlePromotionClick(item)"
        >
          <img :src="item.activityImage" alt="优惠活动" class="promotion-heart-img">
          <div class="promotion-content">
            {{ item.activityName }}
          </div>
          <div class="promotion-arrow">
            <span class="promotion-desc">{{ item.activityDec }}</span>
            <i class="arrow-icon"></i>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
defineProps({
  promotionList: {
    type: Array,
    default: () => []
  },
  bizCode: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['promotion-click'])

const handlePromotionClick = (item) => {
  emit('promotion-click', item)
}
</script>

<style scoped lang="less">
.promotion-section {
  background-color: #FFFFFF;
  padding: 5px 0;
  box-sizing: border-box;
}

.promotion-activity {
  background: linear-gradient(90deg, #FF6362 0%, #FFBE7A 100%);
  padding: 10px;

  .promotion-activity-title {
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .promotion-activity-content {
    display: flex;
    align-items: center;
    border-radius: 8px;
    padding: 13px;
    background: linear-gradient(90deg, #FFEEEB 0%, #FFFAF9 100%);

    .promotion-item {
      display: flex;
      align-items: center;
      width: 100%;
      margin-bottom: 15px;
      border-bottom: 1px solid #f5f5f5;
      cursor: pointer;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
      }

      .promotion-heart-img {
        width: 25px;
        height: 20px;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .promotion-content {
        flex: 1;
        font-size: 14px;
        color: #171E24;
        font-weight: 500;
        line-height: 1.4;
      }

      .promotion-arrow {
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .promotion-desc {
          font-size: 14px;
          color: var(--wo-biz-theme-color);
          margin-right: 8px;
        }

        .arrow-icon {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-top: 2px solid var(--wo-biz-theme-color);
          border-right: 2px solid var(--wo-biz-theme-color);
          transform: rotate(45deg);
        }
      }
    }
  }
}
</style>
