<template>
  <section class="spec-section">
    <div class="spec-wrapper" @click="handleSpecClick">
      <img src="./assets/round_check.png" alt="" class="spec-icon" />
      <div class="spec-main">
        <div class="spec-selected">
          <span class="spec-label">已选：</span>
          <span class="spec-value">{{ selectedSpec }}</span>
          <img src="../../static/images/arrow-right-gray.png" alt="" class="arrow-icon" />
        </div>

        <!-- 规格选项 -->
        <div class="spec-options-wrapper" v-if="bizCode !== 'zq'">
          <div class="spec-options" ref="specOptionsRef">
            <div
              v-for="spec in specOptions"
              :key="spec.id"
              class="spec-option"
              :class="{ 'is-active': spec.selected, 'is-disabled': spec.disabled, 'active': spec.selected }"
              @click.stop="handleSelectSpec(spec)"
            >
              <img v-if="spec.image" :src="spec.image" alt="" class="spec-image" />
              <img v-if="spec.selected" src="./assets/diagonal-hook.png" alt="" class="spec-check" />
            </div>
          </div>
          <div class="spec-count">共{{ specOptions.length }}款可选</div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { getBizCode } from '@/utils/curEnv'
import { ref } from 'vue'

defineProps({
  selectedSpec: {
    type: String,
    default: ''
  },
  specOptions: {
    type: Array,
    default: () => []
  }
})

const bizCode = getBizCode()

const emit = defineEmits(['spec-click', 'select-spec'])

const specOptionsRef = ref(null)

const handleSpecClick = () => {
  emit('spec-click')
}

const handleSelectSpec = (spec) => {
  emit('select-spec', spec)
}

defineExpose({
  specOptionsRef
})
</script>

<style scoped lang="less">
.spec-section {
  background-color: #FFFFFF;
  padding: 5px 17px;
  box-sizing: border-box;
}

.spec-wrapper {
  display: flex;
  align-items: flex-start;
  cursor: pointer;

  .spec-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    margin-top: 2px;
  }

  .spec-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .spec-selected {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    margin-bottom: 12px;

    .spec-label {
      font-size: 13px;
      color: #4A5568;
      margin-right: 4px;
    }

    .spec-value {
      font-size: 13px;
      color: #171E24;
      flex: 1;
    }

    .arrow-icon {
      width: 5px;
      height: 9px;
    }
  }
}

.spec-options-wrapper {
  overflow: hidden;
  display: flex;
  align-items: center;

  .spec-options {
    flex: 1;
    display: flex;
    gap: 12px;
    overflow-x: auto;
    overflow-y: hidden;

    &::-webkit-scrollbar {
      height: 0px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    .spec-option {
      position: relative;
      width: 60px;
      height: 60px;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      border: 2px solid transparent;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;

      &.is-active {
        border-color: var(--wo-biz-theme-color);
      }

      &.active {
        border-color: var(--wo-biz-theme-color);
      }

      &.is-disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .spec-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .spec-name {
        font-size: 11px;
        color: #171E24;
        text-align: center;
        padding: 2px;
        overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
      }

      .spec-check {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 16px;
        height: 16px;
      }
    }
  }

  .spec-count {
    flex-shrink: 0;
    font-size: 12px;
    color: #999999;
    margin-left: 5px;
  }
}
</style>
