<!--
  商品状态提示组件
  功能：显示商品各种状态的提示信息
  特性：
  - 支持多种商品状态提示（下架、无货、无权限等）
  - 固定在页面底部显示
  - 带有滑入动画效果
  - 半透明背景和毛玻璃效果
  - 根据不同状态显示相应提示内容
-->
<template>
  <div v-if="isDataGet" class="status-tips-overlay">
    <div class="status-tips-container">
      <!-- 商品已下架提示 -->
      <div
        class="status-tip tips-state"
        v-if="shouldShowOffSaleTip"
      >
        <div class="tip-content">
          <div class="tip-title">商品已下架</div>
          <div class="tip-message">该商品已下架，请选购其他商品!</div>
        </div>
      </div>

      <!-- 暂时无货提示 -->
      <div
        class="status-tip tips-stock"
        v-if="shouldShowStockTip"
      >
        <div class="tip-content">
          <div class="tip-title">暂时无货</div>
          <div class="tip-message">所选地区暂时无货，非常抱歉！</div>
        </div>
      </div>

      <!-- 无购买资格提示 -->
      <div
        class="status-tip tips-permission"
        v-if="shouldShowPermissionTip"
      >
        <div class="tip-content">
          <div class="tip-title">无购买资格</div>
          <div class="tip-message">您暂无购买资格，非常抱歉！</div>
        </div>
      </div>

      <!-- 区域限制提示 -->
      <div
        class="status-tip tips-region"
        v-if="shouldShowRegionTip"
      >
        <div class="tip-content">
          <div class="tip-title">区域限制</div>
          <div class="tip-message">抱歉，此商品在所选区域暂不支持销售!</div>
        </div>
      </div>

      <!-- 限购商品提示 -->
      <div
        class="status-tip tips-limit"
        v-if="shouldShowLimitTip"
      >
        <div class="tip-content">
          <div class="tip-title">限购商品</div>
          <div class="tip-message">该商品限购，请选购其他商品!</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// 组件属性定义
const props = defineProps({
  // 是否已获取到数据
  isDataGet: {
    type: Boolean,
    default: false
  },
  // 商品是否在售
  onSaleState: {
    type: Boolean,
    default: true
  },
  // 商品是否有库存
  stockState: {
    type: Boolean,
    default: true
  },
  // 用户是否有购买权限
  userStatus: {
    type: Boolean,
    default: true
  },
  // 区域销售状态
  regionalSalesState: {
    type: Boolean,
    default: true
  },
  // 限购状态
  limitState: {
    type: Boolean,
    default: true
  }
})

// 计算属性 - 是否显示商品下架提示
const shouldShowOffSaleTip = computed(() => {
  return !props.onSaleState
})

// 计算属性 - 是否显示库存不足提示
const shouldShowStockTip = computed(() => {
  return props.onSaleState && !props.stockState
})

// 计算属性 - 是否显示权限不足提示
const shouldShowPermissionTip = computed(() => {
  return !props.userStatus
})

// 计算属性 - 是否显示区域限制提示
const shouldShowRegionTip = computed(() => {
  return !props.regionalSalesState
})

// 计算属性 - 是否显示限购提示
const shouldShowLimitTip = computed(() => {
  return !props.limitState
})
</script>

<style scoped lang="less">
.status-tips-overlay {
  position: fixed;
  bottom: 49px;
  left: 0;
  right: 0;
  z-index: 999;
  pointer-events: none;

  .status-tips-container {
    pointer-events: auto;

    .status-tip {
      display: flex;
      align-items: flex-start;
      background: rgba(0, 0, 0, 0.85);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      margin: 10px 5px;
      padding: 5px 10px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      animation: slideInUp 0.3s ease-out;
      box-sizing: border-box;

      .tip-content {
        flex: 1;

        .tip-title {
          color: #FFFFFF;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 4px;
          line-height: 1.5;
        }

        .tip-message {
          color: rgba(255, 255, 255, 0.8);
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }
}

// 滑入动画
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
