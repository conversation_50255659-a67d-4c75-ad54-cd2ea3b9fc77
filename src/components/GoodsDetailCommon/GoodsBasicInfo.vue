<!--
  商品基本信息组件
  功能：展示商品的基本信息（价格、标题等）
  特性：
  - 显示商品当前价格和原价
  - 商品标题展示（支持多行截断）
  - 价格对比显示（原价划线）
  - 响应式布局设计
-->
<template>
  <section class="basic-info-section">
    <!-- 价格区域 -->
    <div class="price-wrapper">
      <PriceDisplay
        :price="goodsInfo.price"
        size="large"
        color="orange"
      />
      <span
        v-if="shouldShowOriginalPrice"
        class="price-original"
      >
        ¥{{ formattedOriginalPrice }}
      </span>
    </div>

    <!-- 商品标题 -->
    <div class="title">
      {{ goodsInfo.name }}
    </div>
  </section>
</template>

<script setup>
import { computed } from 'vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'

// 组件属性定义
const props = defineProps({
  // 商品信息对象
  goodsInfo: {
    type: Object,
    required: true,
    default: () => ({
      name: '',
      price: 0,
      originalPrice: 0,
      imageUrl: ''
    })
  }
})

// 计算属性 - 是否显示原价
const shouldShowOriginalPrice = computed(() => {
  return props.goodsInfo.originalPrice &&
         props.goodsInfo.originalPrice > props.goodsInfo.price
})

// 计算属性 - 格式化原价显示
const formattedOriginalPrice = computed(() => {
  if (!props.goodsInfo.originalPrice) return ''
  return (props.goodsInfo.originalPrice / 100).toFixed(2)
})
</script>

<style scoped lang="less">
.basic-info-section {
  background-color: #FFFFFF;
  padding: 10px 17px;
  box-sizing: border-box;
}

.price-wrapper {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 10px;

  .price-original {
    font-size: 14px;
    color: #999999;
    text-decoration: line-through;
  }
}

.title {
  font-size: 16px;
  color: #171E24;
  font-weight: 500;
  line-height: 1.5;
  display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
}
</style>
