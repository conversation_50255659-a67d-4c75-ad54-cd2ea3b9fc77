<!--
  省分筛选组件
  功能：ZQ业务的省分商品筛选功能
  特性：
  - 根据用户角色类型控制显示
  - 支持自定义筛选标题
  - 懒加载筛选器组件
  - 筛选结果回调处理
  - 企业管理员权限控制
-->
<template>
  <div>
    <!-- 筛选触发按钮 -->
    <div
      class="global-filtering"
      @click="handleFilterClick"
      v-if="shouldShow"
    >
      <div class="global-filtering-title">{{ title }}</div>
      <img
        class="global-filtering-icon"
        src="@/static/images/filtering.png"
        alt="筛选"
      />
    </div>

    <!-- 省分服务选择器（懒加载） -->
    <ZqSelectFilter
      v-if="showDrawer"
      v-model="showDrawer"
      @confirm="handleSelectionConfirm"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import ZqSelectFilter from '@components/ZQCommon/ZQSelectFilter/ZQSelectFilter.vue'
import { getEnterpriseManagerInfo } from '@/utils/zqInfo'

// 组件属性定义
const props = defineProps({
  // 筛选按钮标题
  title: {
    type: String,
    default: '省分在售商品'
  },
  // 用户角色类型（可选，用于覆盖自动获取的角色）
  roleType: {
    type: String,
    default: ''
  },
  // 是否显示筛选组件
  visible: {
    type: Boolean,
    default: true
  }
})

// 事件定义
const emit = defineEmits(['confirm'])

// 响应式数据
const showDrawer = ref(false)

// 计算属性 - 是否显示筛选按钮（仅企业管理员可见）
const shouldShow = computed(() => {
  if (!props.visible) return false

  // 优先使用传入的角色类型
  if (props.roleType) {
    return props.roleType === '4'
  }

  // 从工具函数获取用户角色信息
  const enterpriseInfo = getEnterpriseManagerInfo()
  const roleType = enterpriseInfo?.roleType || ''

  // 角色类型为 '4' 的用户（企业管理员）才能看到筛选功能
  return roleType === '4'
})

// 筛选按钮点击处理
const handleFilterClick = () => {
  showDrawer.value = true
}

// 筛选选择确认处理
const handleSelectionConfirm = (selection) => {
  emit('confirm', selection)
}
</script>

<style scoped lang="less">
.global-filtering {
  flex-shrink: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10px;
  background: #fff;

  .global-filtering-title {
    font-size: 15px;
    color: #5A6066;
  }

  .global-filtering-icon {
    margin-left: 10px;
    width: 18px;
    height: 18px;
  }
}
</style>
