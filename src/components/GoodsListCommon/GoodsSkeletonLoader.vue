<!--
  商品列表骨架屏组件
  功能：商品列表加载时的骨架屏占位
  特性：
  - 支持列表和瀑布流两种布局模式
  - 可配置骨架屏数量
  - 流畅的加载动画效果
  - 完整模拟商品卡片结构
  - 响应式设计适配不同布局
-->
<template>
  <div class="skeleton-container">
    <!-- 列表模式骨架屏 -->
    <div v-if="!isWaterfall" class="list-skeleton">
      <div
        v-for="i in skeletonCount"
        :key="i"
        class="skeleton-item"
      >
        <div class="skeleton-image">
          <div class="skeleton-block"></div>
        </div>
        <div class="skeleton-info">
          <div class="skeleton-info-main">
            <div class="skeleton-title"></div>
            <div class="skeleton-spec"></div>
          </div>
          <div class="skeleton-info-footer">
            <div class="skeleton-price"></div>
            <div class="skeleton-cart">
              <div class="skeleton-block"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 瀑布流模式骨架屏 -->
    <div v-else class="waterfall-skeleton">
      <div class="skeleton-grid">
        <div
          v-for="i in skeletonCount"
          :key="i"
          class="skeleton-item"
        >
          <div class="skeleton-image">
            <div class="skeleton-block"></div>
          </div>
          <div class="skeleton-content">
            <div class="skeleton-title"></div>
            <div class="skeleton-spec"></div>
            <div class="skeleton-sales"></div>
            <div class="skeleton-footer">
              <div class="skeleton-price"></div>
              <div class="skeleton-cart">
                <div class="skeleton-block"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 组件属性定义
defineProps({
  // 是否为瀑布流布局模式
  isWaterfall: {
    type: Boolean,
    default: false
  },
  // 骨架屏显示数量
  skeletonCount: {
    type: Number,
    default: 3
  }
})
</script>

<style scoped lang="less">
.skeleton-container {
  margin-top: 10px;

  .skeleton-block {
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
    width: 100%;
    height: 100%;
  }

  @keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }

  .list-skeleton {
    .skeleton-item {
      display: flex;
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 12px;

      .skeleton-image {
        width: 85px;
        height: 85px;
        flex-shrink: 0;
      }

      .skeleton-info {
        flex: 1;
        margin-left: 8px;
        padding-top: 8px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .skeleton-info-main {
          .skeleton-title {
            height: 13px;
            margin-bottom: 6px;
            border-radius: 4px;
            width: 80%;
          }

          .skeleton-spec {
            height: 11px;
            margin: 4px 0 8px 0;
            border-radius: 4px;
            width: 60%;
          }
        }

        .skeleton-info-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-bottom: 8px;

          .skeleton-price {
            height: 12px;
            border-radius: 4px;
            width: 40%;
          }

          .skeleton-cart {
            width: 25px;
            height: 25px;
          }
        }
      }
    }
  }

  .waterfall-skeleton {
    .skeleton-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
    }

    .skeleton-item {
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;

      .skeleton-image {
        width: 100%;
        height: 120px;
        border-radius: 8px 8px 0 0;
      }

      .skeleton-content {
        padding: 10px;

        .skeleton-title {
          height: 13px;
          margin-bottom: 6px;
          border-radius: 4px;
          width: 90%;
        }

        .skeleton-spec {
          height: 11px;
          margin: 4px 0;
          border-radius: 4px;
          width: 70%;
        }

        .skeleton-sales {
          height: 11px;
          margin: 4px 0;
          border-radius: 4px;
          width: 50%;
        }

        .skeleton-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 8px;

          .skeleton-price {
            height: 12px;
            border-radius: 4px;
            width: 50%;
          }

          .skeleton-cart {
            width: 25px;
            height: 25px;
          }
        }
      }
    }
  }

  .skeleton-title,
  .skeleton-spec,
  .skeleton-sales,
  .skeleton-price {
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
  }
}
</style>
