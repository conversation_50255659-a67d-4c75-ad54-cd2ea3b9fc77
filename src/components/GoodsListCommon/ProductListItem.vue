<!--
  商品列表项组件
  功能：商品列表中的单个商品展示卡片
  特性：
  - 支持商品图片、标题、规格、价格展示
  - 根据业务类型显示不同的价格模式
  - 支持快速加购物车功能
  - 显示销量信息（非ZQ业务）
  - 响应式布局和点击交互
-->
<template>
  <li class="goods-item" @click="handleItemClick">
    <!-- 商品图片 -->
    <div class="goods-image">
      <img
        :src="item.showImageUrl"
        :alt="item.name"
        loading="lazy"
        width="85"
        height="85"
      />
    </div>

    <!-- 商品信息 -->
    <div class="goods-info">
      <div class="goods-info-main">
        <h3 class="goods-title">{{ item.name }}</h3>
        <p class="goods-spec">{{ formattedParams }}</p>
      </div>

      <div class="goods-info-footer">
        <!-- 价格和销量区域 -->
        <div class="price-sales">
          <!-- ZQ业务显示价格区间 -->
          <template v-if="shouldShowPriceRange">
            <PriceDisplay
              :high-price="item.highPrice"
              :low-price="item.lowPrice"
              range-label="参考价"
              size="small"
              color="orange"
            />
          </template>

          <!-- 其他业务显示单价 -->
          <template v-else>
            <PriceDisplay
              :price="item.price"
              size="small"
              color="orange"
            />
          </template>

          <!-- 销量信息（非ZQ业务） -->
          <span
            class="sales-count"
            v-if="shouldShowSales"
          >
            销量{{ item.realSaleVolume }}件
          </span>
        </div>

        <!-- 快速加购物车按钮（非ZQ业务） -->
        <button
          v-if="shouldShowCartButton"
          class="cart-btn"
          @click.stop="handleAddCart"
          type="button"
          aria-label="加入购物车"
        >
          <img
            :src="quickCartIcon"
            alt="加入购物车"
            width="25"
            height="25"
          />
        </button>
      </div>
    </div>
  </li>
</template>

<script setup>
import { computed } from 'vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import { getBizCode } from '@utils/curEnv.js'
import woQuickCart from '@/static/images/wo-quick-cart.png'
import jdQuickCart from '@/static/images/jd-quick-cart.png'

// 组件属性定义
const props = defineProps({
  // 商品数据对象
  item: {
    type: Object,
    required: true
  }
})

// 事件定义
const emit = defineEmits(['item-click', 'add-cart'])

// 计算属性 - 业务类型判断
const bizCode = computed(() => getBizCode())
const isZQ = computed(() => bizCode.value === 'zq')

// 计算属性 - 快速购物车图标
const quickCartIcon = computed(() => {
  return bizCode.value === 'ygjd' ? jdQuickCart : woQuickCart
})

// 计算属性 - 格式化商品参数
const formattedParams = computed(() => {
  return Array.isArray(props.item.params) ? props.item.params.join(' ') : ''
})

// 计算属性 - 是否显示价格区间
const shouldShowPriceRange = computed(() => {
  return isZQ.value && (props.item.highPrice || props.item.lowPrice)
})

// 计算属性 - 是否显示销量信息
const shouldShowSales = computed(() => {
  return !isZQ.value
})

// 计算属性 - 是否显示购物车按钮
const shouldShowCartButton = computed(() => {
  return !isZQ.value
})

// 商品点击处理
const handleItemClick = () => {
  emit('item-click', props.item)
}

// 加入购物车处理
const handleAddCart = () => {
  emit('add-cart', props.item)
}
</script>

<style scoped lang="less">
.goods-item {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  cursor: pointer;

  .goods-image {
    width: 85px;
    height: 85px;
    flex-shrink: 0;
    border-radius: 8px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px;
    }
  }

  .goods-info {
    flex: 1;
    margin-left: 8px;
    padding-top: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .goods-info-main {
      .goods-title {
        font-size: 13px;
        color: #333;
        margin: 0;
        font-weight: normal;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.5;
      }

      .goods-spec {
        font-size: 11px;
        color: #666666;
        line-height: 1.5;
        margin: 4px 0 0 0;
      }
    }

    .goods-info-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 8px;

      .price-sales {
        display: flex;
        align-items: center;

        .sales-count {
          font-size: 11px;
          color: #999999;
          margin-left: 12px;
        }
      }

      .cart-btn {
        background: none;
        border: none;
        padding: 0;
        cursor: pointer;

        img {
          width: 25px;
          height: 25px;
        }
      }
    }
  }
}
</style>
