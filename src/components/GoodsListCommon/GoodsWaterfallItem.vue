<template>
  <article class="waterfall-item" @click="handleItemClick">
    <div class="waterfall-card">
      <div class="waterfall-image">
        <img :src="item.showImageUrl" :alt="item.name" loading="lazy" />
      </div>
      <div class="waterfall-content">
        <h3 class="waterfall-title">{{ item.name }}</h3>
        <p class="waterfall-spec">{{ item.params.join(' ') }}</p>
        <p class="waterfall-sales" v-if="!isZQ">销量{{ item.realSaleVolume }}件</p>
        <div class="waterfall-footer">
          <template v-if="isZQ && (item.highPrice || item.lowPrice)">
            <PriceDisplay
              :high-price="item.highPrice"
              :low-price="item.lowPrice"
              range-label="参考价"
              size="small"
              color="orange"
            />
          </template>
          <template v-else>
            <PriceDisplay :price="item.price" size="small" color="orange" />
          </template>
          <button
            v-if="!isZQ"
            class="cart-btn"
            @click.stop="handleAddCart"
            type="button"
            aria-label="加入购物车"
          >
            <img :src="quickCartIcon" alt="" width="25" height="25" />
          </button>
        </div>
      </div>
    </div>
  </article>
</template>

<script setup>
import { toRefs, computed } from 'vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import { getBizCode } from '@utils/curEnv.js'
import woQuickCart from '@/static/images/wo-quick-cart.png'
import jdQuickCart from '@/static/images/jd-quick-cart.png'

const props = defineProps({
  item: {
    type: Object,
    required: true
  }
})

const { item } = toRefs(props)
const quickCartIcon = computed(() => (getBizCode() === 'ygjd' ? jdQuickCart : woQuickCart))

const isZQ = computed(() => getBizCode() === 'zq')

const emit = defineEmits(['item-click', 'add-cart'])

const handleItemClick = () => {
  emit('item-click', item.value)
}

const handleAddCart = () => {
  emit('add-cart', item.value)
}
</script>

<style scoped lang="less">
.waterfall-item {
  break-inside: avoid;
  cursor: pointer;

  .waterfall-card {
    background-color: #FFFFFF;
    overflow: hidden;

    .waterfall-image {
      width: 100%;
      overflow: hidden;
      position: relative;
      border-radius: 8px;

      img {
        width: 100%;
        height: auto;
        object-fit: cover;
        border-radius: 8px;
      }
    }

    .waterfall-content {
      margin-top: 10px;

      .waterfall-title {
        font-size: 13px;
        color: #333333;
        margin: 0;
        font-weight: normal;
        line-height: 1.5;
        display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
      }

      .waterfall-spec {
        font-size: 11px;
        color: #666666;
        line-height: 1.5;
        margin: 4px 0 0 0;
      }

      .waterfall-sales {
        font-size: 11px;
        color: #999999;
        margin: 4px 0 0 0;
      }

      .waterfall-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;

        .cart-btn {
          background: none;
          border: none;
          padding: 0;
          cursor: pointer;

          img {
            width: 25px;
            height: 25px;
          }
        }
      }
    }
  }
}
</style>
