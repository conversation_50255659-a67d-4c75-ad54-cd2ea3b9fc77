/**
 * @fileoverview 金额处理工具函数
 * @description 提供金额格式化、转换和价格计算相关的工具函数
 */

/**
 * 将金额从分转换为元并分离整数和小数部分
 * @param {number|string} amt - 金额，单位为分
 * @returns {string[]} 返回包含元和分的数组，例如：['1', '23']
 * @example
 * splitAmt(123) // ['1', '23']
 * splitAmt(3) // ['0', '03']
 */
export const splitAmt = (amt) => {
  const amtN = Number(amt) / 100
  return splitAmtFromYuan(amtN)
}

/**
 * 将金额从元分离整数和小数部分
 * @param {number|string} amt - 金额，单位为元
 * @returns {string[]} 返回包含元和分的数组，例如：['1', '23']
 * @example
 * splitAmtFromYuan(1.23) // ['1', '23']
 * splitAmtFromYuan(0.03) // ['0', '03']
 */
export const splitAmtFromYuan = (amt) => {
  const amtN = Number(amt)
  const amtS = amtN.toFixed(2)
  return amtS.split('.')
}

/**
 * 将金额从分转换为元
 * @param {number|string} amt - 金额，单位为分
 * @returns {string} 返回格式化后的金额字符串，保留两位小数
 * @example
 * fenToYuan(123) // '1.23'
 * fenToYuan(50) // '0.50'
 */
export const fenToYuan = (amt) => {
  return (amt / 100).toFixed(2)
}

/**
 * 计算商品的售价和划线价
 * @param {Object} goods - 商品对象
 * @param {Array} goods.skuList - SKU列表
 * @returns {Array<number>} 返回价格数组，包含售价和划线价
 * @example
 * priceCompute({ skuList: [{ price: 100, crossedPrice: 120 }] })
 * // [100, 120]
 */
export const priceCompute = (goods) => {
  const sku = goods && goods.skuList && goods.skuList[0]
    ? goods.skuList[0]
    : {}
  return priceComputeFromSku(sku)
}

/**
 * 根据SKU信息计算价格数组
 * @param {Object} sku - SKU对象
 * @param {Array} [sku.skuPromotionList] - 促销活动列表
 * @param {number} [sku.price] - SKU价格
 * @param {number} [sku.crossedPrice] - 划线价
 * @returns {Array<number>} 返回价格数组，按优先级排序：活动价、SKU价、划线价
 * @description 价格数组按优先级包含：活动价(如果有)、SKU价、划线价(如果有)
 *              使用时按顺序获取前两个(第一个为展示价、第二个为划线价)
 *              可能的组合：[活动价, SKU价] 或 [SKU价, 划线价] 或 [SKU价]
 * @example
 * priceComputeFromSku({
 *   skuPromotionList: [{ promotionPrice: 80 }],
 *   price: 100,
 *   crossedPrice: 120
 * }) // [80, 100, 120]
 */
export const priceComputeFromSku = (sku) => {
  const priceData = []
  const skuPromotionList = sku.skuPromotionList

  // 获取活动价格
  const promotionPrice = (skuPromotionList && skuPromotionList.length > 0)
    ? skuPromotionList[0].promotionPrice
    : ''
  if (promotionPrice) {
    priceData.push(promotionPrice)
  }

  // 获取SKU价格
  const skuPrice = sku.price
  if (skuPrice) {
    priceData.push(skuPrice)
  }

  // 获取划线价格
  const underlinePrice = sku.crossedPrice
  if (underlinePrice) {
    priceData.push(underlinePrice)
  }

  return priceData
}
