/**
 * @fileoverview 主题管理工具模块
 * @description 提供动态主题切换功能，通过CSS类名控制不同业务场景下的主题样式
 * <AUTHOR>
 * @since 1.0.0
 */

/**
 * 京东主题CSS类名常量
 * @description 定义京东主题对应的CSS类名，用于主题切换
 * @readonly
 * @type {string}
 */
const THEME_CLASS_JD = 'theme-jd'

/**
 * 根据业务编码应用对应主题
 * @description 通过给HTML根节点添加/移除CSS类名来切换主题，覆盖:root中的默认CSS变量
 * @param {string} bizCode - 业务编码标识
 * @example
 * // 应用京东主题
 * applyThemeByBizCode('ygjd')
 * 
 * // 应用默认主题（沃橙主题）
 * applyThemeByBizCode('default')
 * 
 * // 在业务逻辑中使用
 * const currentBizCode = getBizCode()
 * applyThemeByBizCode(currentBizCode)
 */
export function applyThemeByBizCode(bizCode) {
  const rootEl = document.documentElement
  
  // 安全检查：确保DOM元素存在
  if (!rootEl) return

  // 根据业务编码切换主题
  if (bizCode === 'ygjd') {
    // 应用京东主题
    if (!rootEl.classList.contains(THEME_CLASS_JD)) {
      rootEl.classList.add(THEME_CLASS_JD)
    }
  } else {
    // 移除京东主题，恢复默认主题
    if (rootEl.classList.contains(THEME_CLASS_JD)) {
      rootEl.classList.remove(THEME_CLASS_JD)
    }
  }
}

/**
 * 初始化应用主题
 * @description 在应用启动时调用，根据当前业务编码设置初始主题
 * @param {Function} getBizCode - 获取业务编码的同步函数
 * @returns {string} getBizCode.return - 当前业务编码
 * @example
 * // 在应用入口文件中初始化主题
 * import { getBizCode } from '@/utils/curEnv'
 * import { initTheme } from '@/utils/themeManager'
 * 
 * // 应用启动时初始化主题
 * initTheme(getBizCode)
 * 
 * // 或者传入自定义的获取函数
 * initTheme(() => {
 *   return localStorage.getItem('bizCode') || 'default'
 * })
 */
export function initTheme(getBizCode) {
  try {
    // 验证参数类型并获取业务编码
    const code = typeof getBizCode === 'function' ? getBizCode() : undefined
    
    // 如果获取到有效的业务编码，则应用对应主题
    if (code) {
      applyThemeByBizCode(code)
    }
  } catch (e) {
    // 静默处理错误，避免影响应用启动
    console.warn('[themeManager] initTheme error:', e)
  }
}