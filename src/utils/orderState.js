/**
 * @fileoverview 订单状态管理工具
 * @description 提供订单状态码与中文描述的映射转换功能
 * <AUTHOR>
 * @since 1.0.0
 */

/**
 * 订单状态映射表
 * @description 定义订单状态码与对应中文描述的映射关系
 * @readonly
 * @type {Object<string, string>}
 */
const ORDER_STATE_MAP = {
  '0': '待付款',
  '1': '待发货',
  '2': '已取消',
  '3': '待发货',
  '4': '部分发货',
  '5': '配送中',
  '6': '部分撤销',
  '7': '拒收',
  '8': '已撤销',
  '9': '已签收',
  '10': '已退款',
  '11': '部分退款',
  '12': '部分退款中',
  '-1': '已删除'
}

/**
 * 订单状态码转换为中文描述
 * @description 根据订单状态码返回对应的中文描述文本
 * @param {string|number} state - 订单状态码
 * @returns {string} 状态的中文描述，未找到对应状态时返回空字符串
 * @example
 * // 基本用法
 * console.log(orderState('0')) // '待付款'
 * console.log(orderState(9)) // '已签收'
 * console.log(orderState('999')) // '' (未知状态)
 * 
 * // 在组件中使用
 * const statusText = orderState(order.status)
 */
export default function orderState(state) {
  return ORDER_STATE_MAP[String(state)] || ''
}
