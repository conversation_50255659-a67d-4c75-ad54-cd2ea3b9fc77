/**
 * 图片压缩工具模块
 * 提供图片文件压缩功能，支持质量压缩和尺寸缩放
 * @fileoverview 图片压缩工具，支持多种图片格式的压缩处理
 */

/**
 * 图片压缩状态枚举
 * @readonly
 * @enum {number}
 */
const STATUS = {
  /** 文件大小未达到压缩阈值 */
  NOT_REACH_COMPRESS_THRESHOLD: 0,
  /** 图片压缩成功 */
  COMPRESS_SUCCESS: 1,
  /** 非图片文件不进行压缩 */
  NOT_IMAGE_FILE: 2,
  /** 当前浏览器不支持图片压缩 */
  NOT_SUPPORT_COMPRESS: 3,
  /** 图片压缩过程出错 */
  COMPRESS_ERROR: 4
}

/**
 * 状态码对应的消息映射
 * @readonly
 * @type {Object<number, string>}
 */
const STATUS_MAPPING_MSG = {
  0: '文件大小未达到压缩阈值',
  1: '图片压缩成功',
  2: '非图片文件不进行压缩',
  3: '当前浏览器不支持图片压缩',
  4: '图片压缩过程出错'
}

/**
 * 压缩图片文件
 * @param {Object} props - 压缩配置参数
 * @param {File} props.file - 待压缩的文件对象
 * @param {number} [props.threshold=2] - 压缩阈值，单位MB，默认2MB
 * @param {number} [props.quality=0.5] - 压缩质量，范围0-1，默认0.5
 * @param {boolean} [props.shrink=false] - 是否需要缩放图片尺寸
 * @param {number} [props.shrinkMaxWidth=1920] - 缩放时的最大宽度，默认1920px
 * @param {number} [props.shrinkMaxHeight=1080] - 缩放时的最大高度，默认1080px
 * @param {boolean} [props.compressUntilSizeBelowThreshold=false] - 是否持续压缩直到文件大小低于阈值
 * @returns {Promise<{file: File, isCompressed: boolean, status: number, msg: string}>} 压缩结果
 * @example
 * // 基本压缩
 * const result = await compressImageFile({ file: imageFile })
 * if (result.status === 1) {
 *   console.log('压缩成功', result.file)
 * }
 * 
 * // 高质量压缩并缩放
 * const result = await compressImageFile({
 *   file: imageFile,
 *   quality: 0.8,
 *   shrink: true,
 *   shrinkMaxWidth: 1200,
 *   shrinkMaxHeight: 800
 * })
 */
const compressImageFile = (props) => {
  const {
    file,
    threshold = 2,
    quality = 0.5,
    shrink = false,
    shrinkMaxWidth = 1920,
    shrinkMaxHeight = 1080,
    compressUntilSizeBelowThreshold = false
  } = props

  // 支持的图片格式列表
  const isAccept = [
    'image/png',
    'image/jpeg',
    'image/jpg',
    'image/gif',
    'image/bmp',
    'image/tiff',
    'image/webp',
    'image/svg+xml',
    'image/heic'
  ]

  // 检查文件类型是否为支持的图片格式
  const isFileTypeAccepted = isAccept.indexOf(file.type) !== -1
  if (!isFileTypeAccepted) {
    return Promise.resolve({
      file: file,
      isCompressed: false,
      status: STATUS.NOT_IMAGE_FILE,
      msg: STATUS_MAPPING_MSG[STATUS.NOT_IMAGE_FILE]
    })
  }

  // 计算文件大小（MB）
  const fileSize = file.size / 1024 / 1024

  // 检查文件大小是否达到压缩阈值
  if (fileSize < threshold) {
    return Promise.resolve({
      file: file,
      isCompressed: false,
      status: STATUS.NOT_REACH_COMPRESS_THRESHOLD,
      msg: STATUS_MAPPING_MSG[STATUS.NOT_REACH_COMPRESS_THRESHOLD]
    })
  }

  // 检查浏览器是否支持FileReader API
  if (typeof FileReader === 'undefined') {
    return Promise.resolve({
      file: file,
      isCompressed: false,
      status: STATUS.NOT_SUPPORT_COMPRESS,
      msg: STATUS_MAPPING_MSG[STATUS.NOT_SUPPORT_COMPRESS]
    })
  }

  // 压缩错误时的统一返回对象
  const compressErrorContent = {
    file: file,
    isCompressed: false,
    status: STATUS.COMPRESS_ERROR,
    msg: STATUS_MAPPING_MSG[STATUS.COMPRESS_ERROR]
  }

  return new Promise((resolve) => {
    try {
      // 创建文件读取器
      const reader = new FileReader()
      // 将文件读取为Data URL格式
      reader.readAsDataURL(file)
      
      reader.onload = (event) => {
        // 创建图片对象
        const img = new Image()
        img.src = event.target?.result || reader?.result || ''
        
        img.onload = () => {
          // 创建Canvas画布
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          
          // 检查Canvas支持性
          if (!ctx) {
            return resolve(compressErrorContent)
          }
          // 获取原始图片尺寸
          const originImageWidth = img.width
          const originImageHeight = img.height
          
          // 计算最大尺寸比例
          const ratio = (shrinkMaxWidth || 1920) / (shrinkMaxHeight || 1080)
          
          // 初始化目标尺寸为原始尺寸
          let targetWidth = originImageWidth
          let targetHeight = originImageHeight
          
          // 如果需要缩放且图片超出最大尺寸限制
          if (
            shrink &&
            (originImageWidth > shrinkMaxWidth || originImageHeight > shrinkMaxHeight)
          ) {
            // 根据宽高比确定缩放方式
            if (originImageWidth / originImageHeight > ratio) {
              // 宽度超限，以宽度为准进行等比缩放
              targetWidth = shrinkMaxWidth
              targetHeight = Math.round(shrinkMaxWidth * (originImageHeight / originImageWidth))
            } else {
              // 高度超限，以高度为准进行等比缩放
              targetHeight = shrinkMaxHeight
              targetWidth = Math.round(shrinkMaxHeight * (originImageWidth / originImageHeight))
            }
          }
          
          // 设置Canvas尺寸
          canvas.width = targetWidth
          canvas.height = targetHeight
          
          // 清除画布内容
          ctx.clearRect(0, 0, targetWidth, targetHeight)
          
          // 绘制缩放后的图片
          ctx.drawImage(img, 0, 0, targetWidth, targetHeight)
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const newFile = new File([blob], file.name, { type: file.type })
                
                // 检查是否需要继续压缩
                if (
                  compressUntilSizeBelowThreshold &&
                  newFile.size / 1024 / 1024 >= threshold &&
                  quality - 0.2 >= 0.2
                ) {
                  // 递归压缩，降低质量直到达到阈值
                  compressImageFile({
                    file: newFile,
                    threshold: threshold,
                    quality: quality - 0.2,
                    shrink: shrink,
                    shrinkMaxWidth: shrinkMaxWidth,
                    shrinkMaxHeight: shrinkMaxHeight,
                    compressUntilSizeBelowThreshold: compressUntilSizeBelowThreshold
                  }).then((res) => {
                    resolve(res)
                  })
                } else {
                  // 压缩完成，返回结果
                  return resolve({
                    file: newFile,
                    isCompressed: true,
                    status: STATUS.COMPRESS_SUCCESS,
                    msg: STATUS_MAPPING_MSG[STATUS.COMPRESS_SUCCESS]
                  })
                }
              } else {
                // Blob生成失败
                return resolve(compressErrorContent)
              }
            },
            file.type,
            quality
          )
        }
        
        img.onerror = () => {
          // 图片加载失败
          return resolve(compressErrorContent)
        }
      }
      
      reader.onerror = () => {
        // 文件读取失败
        return resolve(compressErrorContent)
      }
    } catch (error) {
      // 捕获其他异常
      return resolve(compressErrorContent)
    }
  })
}

export default compressImageFile
