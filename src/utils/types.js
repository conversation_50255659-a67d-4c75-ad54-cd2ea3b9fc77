/**
 * @fileoverview 商品识别码和渠道商城相关常量定义
 * @description 定义了各种商品识别码前缀和渠道商城数据存储键名
 * <AUTHOR> Team
 * @since 1.0.0
 */

/**
 * 京东商品识别码前缀
 * @description 用于标识京东平台的商品，作为商品ID的前缀
 * @type {string}
 * @constant
 * @example
 * // 生成京东商品完整ID
 * const jdProductId = JD_GOODS_CODE + '12345678'
 * console.log(jdProductId) // 'jd_12345678'
 */
export const JD_GOODS_CODE = 'jd_'

/**
 * 渠道商城地址选择层级数据存储键
 * @description 用于本地存储中保存地址选择的层级数据（省市区等）
 * @type {string}
 * @constant
 * @example
 * // 存储地址数据
 * localStorage.setItem(PS_CCMS_AREA_LIST_DATA, JSON.stringify(areaData))
 * 
 * // 获取地址数据
 * const areaData = JSON.parse(localStorage.getItem(PS_CCMS_AREA_LIST_DATA) || '[]')
 */
export const PS_CCMS_AREA_LIST_DATA = 'PS_CCMS_AREA_LIST_DATA'

/**
 * 渠道商城分类选择层级数据存储键
 * @description 用于本地存储中保存商品分类的层级数据
 * @type {string}
 * @constant
 * @example
 * // 存储分类数据
 * localStorage.setItem(PS_CCMS_CATEGORY_LIST_DATA, JSON.stringify(categoryData))
 * 
 * // 获取分类数据
 * const categoryData = JSON.parse(localStorage.getItem(PS_CCMS_CATEGORY_LIST_DATA) || '[]')
 */
export const PS_CCMS_CATEGORY_LIST_DATA = 'PS_CCMS_CATEGORY_LIST_DATA'

/**
 * 渠道商城品牌选择层级数据存储键
 * @description 用于本地存储中保存品牌选择的层级数据
 * @type {string}
 * @constant
 * @example
 * // 存储品牌数据
 * localStorage.setItem(PS_CCMS_BRAND_LIST_DATA, JSON.stringify(brandData))
 * 
 * // 获取品牌数据
 * const brandData = JSON.parse(localStorage.getItem(PS_CCMS_BRAND_LIST_DATA) || '[]')
 */
export const PS_CCMS_BRAND_LIST_DATA = 'PS_CCMS_BRAND_LIST_DATA'
