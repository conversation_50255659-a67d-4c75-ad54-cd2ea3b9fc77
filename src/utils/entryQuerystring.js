/**
 * @fileoverview 入口参数处理工具
 * @description 处理URL查询参数，设置分销业务码、渠道码等相关存储值
 */

import { parse } from 'qs'
import { isWopay, isUnicom, urlAppend } from 'commonkit'
import { getBizCode } from '@/utils/curEnv'
import { queryZqInfo } from '@utils/zqInfo.js'
import { curChannelBiz, curDeveloperId, curDistriBiz, loginType, zqRole } from '@utils/storage.js'

/**
 * 处理入口URL参数并设置相应的存储值
 * @param {string} [search] - URL查询字符串，默认使用当前页面的查询字符串
 * @description 解析URL参数，设置分销业务码、渠道码、发展人ID等存储值，支持特殊路径和渠道的处理逻辑
 * @example
 * // 使用当前页面的查询字符串
 * setEntryQuerystring()
 * 
 * // 使用自定义查询字符串
 * setEntryQuerystring('distri_biz_code=ziying&source=212')
 */
export const setEntryQuerystring = search => {
  // 获取并解析查询字符串
  search = search || window.location.search.substring(1)
  const querystringObject = parse(search)
  const path = window.location.pathname

  // 处理分销业务码参数
  const PS_CCMS_DISTRI_BIZ = querystringObject.distri_biz_code
  if (PS_CCMS_DISTRI_BIZ) {
    curDistriBiz.set(PS_CCMS_DISTRI_BIZ)
  }

  // 特殊路径的分销业务码处理
  if (path.indexOf('/digitalVillage/') >= 0) {
    // 数字乡村路径特殊处理
    curDistriBiz.set('szxc')
  } else if (path.indexOf('/jdzy/') >= 0) {
    // 京东开普勒路径特殊处理
    curDistriBiz.set('jdkpl')
  }

  // 政企渠道特殊处理逻辑
  if (PS_CCMS_DISTRI_BIZ === 'zq') {
    loginType.set('1')
    const role = querystringObject.role
    if (role === '1' || role === '2') {
      zqRole.set(role)
    }
  }

  // 处理渠道业务码
  const PS_CCMS_CHANNEL_BIZ = querystringObject.source ||
                              querystringObject.biz_channel_code ||
                              curChannelBiz.get()

  // 根据不同环境和条件设置渠道码
  if (isWopay) {
    curChannelBiz.set('212') // 沃支付环境
  } else if (isUnicom) {
    curChannelBiz.set('225') // 联通环境
  } else if (PS_CCMS_CHANNEL_BIZ) {
    curChannelBiz.set(PS_CCMS_CHANNEL_BIZ)
  } else {
    curChannelBiz.set('226') // 默认渠道码
  }

  // 处理发展人ID参数
  const PS_CCMS_DEVELOPER_ID = querystringObject.developer_id || querystringObject.developerId
  if (PS_CCMS_DEVELOPER_ID) {
    curDeveloperId.set(PS_CCMS_DEVELOPER_ID)
  }
}


/**
 * 处理URL参数，确保包含必要的业务代码参数
 * @param {Object} to - 目标路由对象，包含路径、查询参数等信息
 * @param {string} to.fullPath - 完整路径
 * @param {string} to.path - 路由路径
 * @param {Object} to.query - 查询参数对象
 * @description 检查并补充URL中缺失的业务参数，特别处理政企商城的isv参数和通用的distri_biz_code参数
 * @example
 * // 在路由守卫中使用
 * router.beforeEach((to, from, next) => {
 *   handleUrlParameters(to)
 *   next()
 * })
 */
export const handleUrlParameters = (to) => {
  const host = window.location.origin
  const path = import.meta.env.VITE_BASE_URL
  const baseUrl = host + path + to.fullPath
  const bizCode = getBizCode()
  let callbackUrl = baseUrl
  let needUpdateUrl = false

  // 政企商城特殊逻辑处理
  if (bizCode === 'zq' && !to.query.isv && to.path !== '/login') {
    const zqInfo = queryZqInfo()
    callbackUrl = urlAppend(baseUrl, {
      distri_biz_code: bizCode,
      isv: zqInfo.isvList && zqInfo.isvList.length > 0 ? zqInfo.isvList[0].isvId : ''
    })
    needUpdateUrl = true
  }
  // 处理普通情况下缺少分销业务码参数
  else if (!to.query.distri_biz_code) {
    callbackUrl = urlAppend(baseUrl, { distri_biz_code: bizCode })
    needUpdateUrl = true
  }

  // 仅在需要时更新URL，避免不必要的历史记录变更
  if (needUpdateUrl) {
    // 保留现有的history.state值，避免Vue Router警告
    history.replaceState(history.state, '', callbackUrl)
  }
}

