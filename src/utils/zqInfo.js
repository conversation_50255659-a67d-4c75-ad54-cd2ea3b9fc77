/**
 * @fileoverview 政企信息处理模块
 * @description 提供政企相关信息的获取和处理功能，包括角色识别、权限判断等
 * <AUTHOR> Team
 * @since 1.0.0
 */
import { getZqInfo } from 'commonkit'
import { zqRole } from '@utils/storage.js'

/**
 * 角色类型常量枚举
 * @description 定义政企系统中的各种角色类型标识
 * @enum {string}
 * @readonly
 */
const ROLE_TYPE = {
  /** 企业经办人角色 */
  ENTERPRISE_MANAGER: '1',
  /** 客户经理角色 */
  CUSTOMER_MANAGER: '2',
  /** 无权限状态 */
  NO_PERMISSION: '-1',
  /** 无法展示状态（多权限但无法确定展示哪个） */
  CANNOT_DISPLAY: '-2',
  /** 白名单用户角色 */
  WHITE_USER: '4'
}

/**
 * 获取客户经理信息
 * @description 从政企信息列表中查找并返回客户经理角色的信息
 * @returns {Object|undefined} 客户经理信息对象，如果不存在则返回undefined
 * @returns {string} return.roleType - 角色类型，值为'2'
 * @returns {string} return.userId - 用户ID
 * @returns {string} return.userName - 用户姓名
 * @returns {string} return.orgCode - 组织代码
 * @example
 * // 获取客户经理信息
 * const managerInfo = getCustomerManagerInfo()
 * if (managerInfo) {
 *   console.log('客户经理:', managerInfo.userName)
 *   console.log('组织代码:', managerInfo.orgCode)
 * }
 */
export const getCustomerManagerInfo = () => {
  const info = getZqInfo() || []
  return info.find(item => item.roleType === ROLE_TYPE.CUSTOMER_MANAGER)
}

/**
 * 获取企业经办人信息
 * @description 从政企信息列表中查找并返回企业经办人或白名单用户角色的信息
 * @returns {Object|undefined} 企业经办人信息对象，如果不存在则返回undefined
 * @returns {string} return.roleType - 角色类型，值为'1'（企业经办人）或'4'（白名单用户）
 * @returns {string} return.userId - 用户ID
 * @returns {string} return.userName - 用户姓名
 * @returns {string} return.orgCode - 组织代码
 * @example
 * // 获取企业经办人信息
 * const enterpriseInfo = getEnterpriseManagerInfo()
 * if (enterpriseInfo) {
 *   console.log('企业经办人:', enterpriseInfo.userName)
 *   console.log('角色类型:', enterpriseInfo.roleType)
 * }
 */
export const getEnterpriseManagerInfo = () => {
  const info = getZqInfo() || []
  return info.find(item => item.roleType === ROLE_TYPE.ENTERPRISE_MANAGER || item.roleType === ROLE_TYPE.WHITE_USER)
}

/**
 * 获取当前用户的政企信息
 * @description 根据session中的权限信息和URL参数，智能判断并返回当前用户的政企角色信息
 * @returns {Object} 政企信息对象
 * @returns {string} return.roleType - 角色类型标识
 * @returns {string} [return.userId] - 用户ID（当有具体角色时）
 * @returns {string} [return.userName] - 用户姓名（当有具体角色时）
 * @returns {string} [return.orgCode] - 组织代码（当有具体角色时）
 * 
 * @description 角色类型说明：
 * - '-2': 无法展示（session有多条权限信息，但没有读取到应展示的权限）
 * - '-1': 无权限（session中无权限信息）
 * - '1': 企业经办人
 * - '2': 客户经理
 * - '4': 白名单用户
 * 
 * @example
 * // 获取当前用户政企信息
 * const zqInfo = queryZqInfo()
 * 
 * switch (zqInfo.roleType) {
 *   case '1':
 *     console.log('当前用户是企业经办人:', zqInfo.userName)
 *     break
 *   case '2':
 *     console.log('当前用户是客户经理:', zqInfo.userName)
 *     break
 *   case '-1':
 *     console.log('用户无政企权限')
 *     break
 *   case '-2':
 *     console.log('无法确定用户角色')
 *     break
 * }
 */
export const queryZqInfo = () => {
  const info = getZqInfo() || []

  // 根据信息条数处理不同情况
  if (info.length === 0) {
    // 无权限
    return { roleType: ROLE_TYPE.NO_PERMISSION }
  }

  if (info.length === 1) {
    // 单个权限，直接返回
    return info[0]
  }

  if (info.length === 2) {
    // 多个权限，根据URL参数决定角色
    const role = zqRole.get()

    if (role === ROLE_TYPE.ENTERPRISE_MANAGER || role === ROLE_TYPE.WHITE_USER) {
      return getEnterpriseManagerInfo()
    }

    if (role === ROLE_TYPE.CUSTOMER_MANAGER) {
      return getCustomerManagerInfo()
    }

    // 未指定角色
    return { roleType: ROLE_TYPE.CANNOT_DISPLAY }
  }

  // 默认返回无法展示
  return { roleType: ROLE_TYPE.CANNOT_DISPLAY }
}
