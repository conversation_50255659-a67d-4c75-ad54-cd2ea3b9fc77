/**
 * @fileoverview 分享功能工具模块
 * @description 提供页面分享相关的工具函数和配置
 * <AUTHOR>
 * @since 1.0.0
 */

import { log } from 'commonkit'
import { copy } from '@/utils/clipboard'
import { getFuLiHuiID } from '@api/interface/flh.js'
import { getBizCode } from '@/utils/curEnv'
import { curDeveloperId } from '@utils/storage.js'
import { showToast } from 'vant'

/**
 * 获取默认分享链接
 * @description 根据当前页面URL和业务代码生成分享链接，支持福利汇等特殊业务场景
 * @async
 * @returns {Promise<string>} 生成的分享链接
 * @example
 * // 基本用法
 * const shareUrl = await getDefaultShareUrl()
 * console.log(shareUrl) // 'https://example.com/page?distri_biz_code=fulihui&developerId=123'
 * 
 * // 在分享功能中使用
 * shareData.link = await getDefaultShareUrl()
 */
export const getDefaultShareUrl = async () => {
  const urlSearch = window.location.search
  const urlQueryStr = urlSearch ? urlSearch.split('?')[1] : ''

  let url = window.location.origin + window.location.pathname + urlSearch
  
  // 检查URL中是否已包含分销业务代码
  if (!(urlQueryStr.indexOf('distri_biz_code') >= 0)) {
    url += url.indexOf('?') >= 0 ? '&' : '?'
    
    const bizCode = getBizCode()
    
    // 福利汇商城特殊处理
    if (bizCode === 'fulihui') {
      let developerId = curDeveloperId.get()
      
      // 如果本地没有developerId，则从接口获取
      if (!developerId) {
        const [err, json] = await getFuLiHuiID({ bizCode })
        if (!err) {
          developerId = json || ''
        }
      }
      
      curDeveloperId.set(developerId)
      const shareUrl = url + 'distri_biz_code=' + bizCode + '&developerId=' + developerId
      log('[SHARE-DATA] 福利汇 shareUrl', shareUrl)
      return shareUrl
    }
    
    // 普通业务场景
    const shareUrl = url + 'distri_biz_code=' + bizCode
    log('[SHARE-DATA] shareUrl', shareUrl)
    return shareUrl
  }
  
  return url
}

/**
 * 分享数据配置对象
 * @description 统一管理分享相关的数据和回调函数
 * @type {Object}
 * @property {string} link - 分享链接
 * @property {string} title - 分享标题
 * @property {string} describe - 分享描述
 * @property {string} picUrl - 分享图片URL
 * @property {Function} next - 分享后续处理函数
 * @property {Function} callback - 分享完成回调函数
 * @example
 * // 设置分享数据
 * shareData.link = 'https://example.com'
 * shareData.title = '商品标题'
 * shareData.describe = '商品描述'
 * shareData.picUrl = 'https://example.com/image.jpg'
 * 
 * // 触发分享
 * shareData.next({ type: 'h5' })
 */
export const shareData = {
  /** @type {string} 分享链接 */
  link: '',
  
  /** @type {string} 分享标题 */
  title: '',
  
  /** @type {string} 分享描述 */
  describe: '',
  
  /** @type {string} 分享图片URL */
  picUrl: '',
  
  /**
   * 分享后续处理函数
   * @description 处理不同平台的分享逻辑
   * @param {Object} options - 分享选项
   * @param {string} options.type - 分享类型 ('h5' | 'weixin')
   * @param {Object} [userData] - 用户数据
   */
  next: (options, userData) => {
    // H5环境下复制链接到剪贴板
    if (options.type === 'h5') {
      copy(shareData.link)
    } else if (options.type === 'weixin') {
      // 微信环境下提示用户手动分享
      showToast('请点击屏幕右上方的按钮进行分享')
    }
  },
  
  /**
   * 分享完成回调函数
   * @description 分享操作完成后的回调处理
   */
  callback: () => {
    // 分享完成后的处理逻辑
  }
}
