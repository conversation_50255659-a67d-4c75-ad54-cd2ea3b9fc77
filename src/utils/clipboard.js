/**
 * @fileoverview 剪贴板操作工具
 * @description 提供文本复制到剪贴板的功能，集成Toast提示
 */

import { showToast } from 'vant'
import useClipboard from 'vue-clipboard3'

/**
 * 复制文本到剪贴板并显示提示信息
 * @param {string} text - 要复制的文本内容
 * @returns {Promise<boolean>} 返回Promise，成功时resolve为true，失败时resolve为false
 * @description 使用vue-clipboard3库实现文本复制功能，并通过vant的Toast组件显示操作结果
 * @example
 * // 复制文本
 * copy('Hello World').then(success => {
 *   if (success) {
 *     console.log('复制成功')
 *   } else {
 *     console.log('复制失败')
 *   }
 * })
 * 
 * // 使用async/await
 * const success = await copy('Hello World')
 */
export const copy = async (text) => {
  const { toClipboard } = useClipboard()
  
  try {
    await toClipboard(text)
    showToast('链接已复制，赶快分享吧！~')
    return true
  } catch (e) {
    showToast('设备不支持复制功能，请手动复制...')
    return false
  }
}
