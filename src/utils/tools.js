/**
 * @fileoverview 工具函数和类库模块
 * @description 提供平台检测、批量请求处理等通用工具功能
 * <AUTHOR>
 * @since 1.0.0
 */

/**
 * 检测Android平台版本
 * @description 判断当前设备是否为Android且版本小于指定版本号
 * @param {number} num - 目标版本号
 * @returns {boolean|undefined} 如果是Android且版本小于指定版本返回true，否则返回undefined
 * @example
 * // 检测是否为Android 5.0以下版本
 * const isOldAndroid = heckPlatform(5)
 * if (isOldAndroid) {
 *   console.log('当前设备为Android 5.0以下版本')
 * }
 * 
 * // 检测是否为Android 7.0以下版本
 * const needPolyfill = heckPlatform(7)
 * if (needPolyfill) {
 *   // 加载polyfill
 * }
 */
export const heckPlatform = num => {
  const ua = navigator.userAgent.toLowerCase()
  
  // 检测是否为Android设备
  if (/android/i.test(navigator.userAgent)) {
    const test = /android\s([\w.]+)/
    const match = test.exec(ua)
    
    // 提取主版本号并比较
    const version = match[1].split('.')[0]
    if (version < num) {
      return true
    }
  }
}

/**
 * 批量请求处理器类
 * @description 高性能批量请求处理器，支持并发控制、错误处理、超时控制和进度回调
 * @class
 * @example
 * // 基本使用
 * const batch = new BatchRequest({
 *   concurrency: 5,
 *   timeout: 10000,
 *   retryCount: 2
 * })
 * 
 * batch.push(() => fetch('/api/data1'))
 * batch.push(() => fetch('/api/data2'))
 * 
 * batch.onProgress = (progress) => {
 *   console.log(`进度: ${progress.progress * 100}%`)
 * }
 * 
 * const { results, errors } = await batch.executeAll()
 */
export class BatchRequest {
  /**
   * 创建批量请求处理器实例
   * @description 初始化批量请求处理器，配置并发控制、超时、重试等参数
   * @param {Object} options - 配置选项
   * @param {number} [options.concurrency=10] - 并发请求数限制
   * @param {number} [options.timeout=30000] - 单个请求超时时间（毫秒）
   * @param {number} [options.retryCount=0] - 失败重试次数
   * @param {boolean} [options.failFast=false] - 是否在首次失败时立即停止
   * @example
   * // 创建具有自定义配置的批量请求处理器
   * const batch = new BatchRequest({
   *   concurrency: 5,
   *   timeout: 15000,
   *   retryCount: 3,
   *   failFast: true
   * })
   */
  constructor(options = {}) {
    this.promises = []
    this.results = []
    this.errors = []
    this.onCompleteCallback = null
    this.onProgressCallback = null
    this.onErrorCallback = null
    
    // 合并默认配置和用户配置
    this.options = {
      concurrency: options.concurrency || 10,
      timeout: options.timeout || 30000,
      retryCount: options.retryCount || 0,
      failFast: options.failFast || false,
      ...options
    }
    
    this.completed = 0
    this.total = 0
    this.isExecuting = false
  }

  /**
   * 添加请求到批次队列
   * @description 将Promise或返回Promise的函数添加到批量处理队列中
   * @param {Promise|Function} promiseOrFn - Promise实例或返回Promise的函数
   * @param {Object} [metadata={}] - 请求的元数据信息
   * @returns {BatchRequest} 返回当前实例以支持链式调用
   * @throws {Error} 当批次正在执行时抛出错误
   * @example
   * // 添加Promise函数
   * batch.push(() => fetch('/api/users'))
   * 
   * // 添加带元数据的请求
   * batch.push(
   *   () => fetch('/api/orders'),
   *   { type: 'orders', priority: 'high' }
   * )
   * 
   * // 链式调用
   * batch
   *   .push(() => fetch('/api/data1'))
   *   .push(() => fetch('/api/data2'))
   */
  push(promiseOrFn, metadata = {}) {
    if (this.isExecuting) {
      throw new Error('Cannot add requests while batch is executing')
    }
    
    this.promises.push({
      executor: typeof promiseOrFn === 'function' ? promiseOrFn : () => promiseOrFn,
      metadata,
      retries: 0
    })
    this.total = this.promises.length
    return this
  }

  /**
   * 设置批量请求完成回调
   * @description 设置所有请求完成后的回调函数，设置后会自动执行批量请求
   * @param {Function} callback - 完成回调函数
   * @param {Array} callback.results - 所有请求的结果数组
   * @param {Array} callback.errors - 所有错误信息数组
   * @example
   * batch.onComplete = (results, errors) => {
   *   console.log('请求完成:', results.length)
   *   console.log('错误数量:', errors.length)
   * }
   */
  set onComplete(callback) {
    this.onCompleteCallback = callback
    this.executeAll()
  }

  /**
   * 设置进度回调
   * @description 设置请求执行进度的回调函数
   * @param {Function} callback - 进度回调函数
   * @param {Object} callback.progress - 进度信息对象
   * @param {number} callback.progress.completed - 已完成的请求数
   * @param {number} callback.progress.total - 总请求数
   * @param {number} callback.progress.progress - 进度百分比（0-1）
   * @param {number} callback.progress.errors - 错误数量
   * @returns {BatchRequest} 返回当前实例以支持链式调用
   * @example
   * batch.onProgress = ({ completed, total, progress, errors }) => {
   *   console.log(`进度: ${Math.round(progress * 100)}% (${completed}/${total})`)
   *   if (errors > 0) console.warn(`错误: ${errors}`)
   * }
   */
  set onProgress(callback) {
    this.onProgressCallback = callback
    return this
  }

  /**
   * 设置错误回调
   * @description 设置单个请求失败时的回调函数
   * @param {Function} callback - 错误回调函数
   * @param {Error} callback.error - 错误对象
   * @param {Object} callback.metadata - 请求的元数据
   * @param {number} callback.index - 请求在队列中的索引
   * @returns {BatchRequest} 返回当前实例以支持链式调用
   * @example
   * batch.onError = (error, metadata, index) => {
   *   console.error(`请求 ${index} 失败:`, error.message)
   *   if (metadata.type) console.log('请求类型:', metadata.type)
   * }
   */
  set onError(callback) {
    this.onErrorCallback = callback
    return this
  }

  /**
   * 创建带超时控制的Promise
   * @description 为Promise添加超时控制，防止请求无限等待
   * @private
   * @param {Promise} promise - 原始Promise
   * @param {number} timeout - 超时时间（毫秒）
   * @returns {Promise} 带超时控制的Promise
   */
  _createTimeoutPromise(promise, timeout) {
    return Promise.race([
      promise,
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Request timeout')), timeout)
      )
    ])
  }

  /**
   * 执行单个请求（支持重试机制）
   * @description 执行单个请求，支持指数退避重试策略
   * @private
   * @param {Object} requestItem - 请求项对象
   * @param {Function} requestItem.executor - 请求执行函数
   * @param {Object} requestItem.metadata - 请求元数据
   * @param {number} requestItem.retries - 当前重试次数
   * @returns {Promise<Object>} 请求结果对象
   */
  async _executeWithRetry(requestItem) {
    const { executor, metadata, retries } = requestItem
    
    try {
      const promise = executor()
      const result = await this._createTimeoutPromise(promise, this.options.timeout)
      return { success: true, result, metadata }
    } catch (error) {
      // 如果还有重试次数，则进行重试
      if (retries < this.options.retryCount) {
        requestItem.retries++
        // 指数退避策略：等待时间随重试次数指数增长
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000))
        return this._executeWithRetry(requestItem)
      }
      return { success: false, error, metadata }
    }
  }

  /**
   * 并发控制执行所有请求
   * @description 使用并发控制策略执行所有队列中的请求
   * @private
   * @returns {Promise<Array>} 所有请求的结果数组
   */
  async _executeConcurrently() {
    const { concurrency } = this.options
    const executing = []
    const results = []

    for (let i = 0; i < this.promises.length; i++) {
      const requestPromise = this._executeWithRetry(this.promises[i])
        .then(result => {
          this.completed++
          
          if (result.success) {
            results[i] = result.result
          } else {
            // 记录错误信息
            this.errors.push({ index: i, error: result.error, metadata: result.metadata })
            
            // 触发错误回调
            if (this.onErrorCallback) {
              this.onErrorCallback(result.error, result.metadata, i)
            }
            
            // 快速失败模式：遇到错误立即停止
            if (this.options.failFast) {
              throw result.error
            }
          }

          // 触发进度回调
          if (this.onProgressCallback) {
            this.onProgressCallback({
              completed: this.completed,
              total: this.total,
              progress: this.completed / this.total,
              errors: this.errors.length
            })
          }

          return result
        })

      executing.push(requestPromise)

      // 并发数控制：当达到并发限制时等待
      if (executing.length >= concurrency) {
        await Promise.race(executing)
        executing.splice(executing.findIndex(p => p.isFulfilled), 1)
      }
    }

    // 等待所有剩余请求完成
    await Promise.allSettled(executing)
    return results
  }

  /**
   * 执行所有批量请求
   * @description 启动批量请求的执行过程，返回所有请求的结果
   * @returns {Promise<Object>} 执行结果对象
   * @returns {Array} return.results - 所有成功请求的结果数组
   * @returns {Array} return.errors - 所有失败请求的错误信息数组
   * @returns {boolean} return.success - 是否所有请求都成功
   * @throws {Error} 当批次正在执行或快速失败模式下遇到错误时抛出
   * @example
   * // 执行批量请求并处理结果
   * try {
   *   const { results, errors, success } = await batch.executeAll()
   *   
   *   if (success) {
   *     console.log('所有请求成功:', results)
   *   } else {
   *     console.log('部分请求失败:', errors)
   *   }
   * } catch (error) {
   *   console.error('批量请求执行失败:', error)
   * }
   */
  async executeAll() {
    if (this.isExecuting) {
      throw new Error('Batch is already executing')
    }

    if (this.promises.length === 0) {
      if (this.onCompleteCallback) {
        this.onCompleteCallback([], [])
      }
      return { results: [], errors: [] }
    }

    this.isExecuting = true
    this.completed = 0
    this.results = []
    this.errors = []

    try {
      this.results = await this._executeConcurrently()
      
      if (this.onCompleteCallback) {
        this.onCompleteCallback(this.results, this.errors)
      }

      return {
        results: this.results,
        errors: this.errors,
        success: this.errors.length === 0
      }
    } catch (error) {
      console.error('批量请求执行失败:', error)
      
      // 即使发生错误也要触发完成回调
      if (this.onCompleteCallback) {
        this.onCompleteCallback(this.results, this.errors)
      }
      
      throw error
    } finally {
      // 确保执行状态被重置
      this.isExecuting = false
    }
  }

  /**
   * 清空批次队列
   * @description 清空所有待执行的请求和结果数据，重置批次状态
   * @returns {BatchRequest} 返回当前实例以支持链式调用
   * @throws {Error} 当批次正在执行时抛出错误
   * @example
   * // 清空当前批次并添加新的请求
   * batch.clear()
   *   .push(() => fetch('/api/new-data1'))
   *   .push(() => fetch('/api/new-data2'))
   */
  clear() {
    if (this.isExecuting) {
      throw new Error('Cannot clear while batch is executing')
    }
    
    // 重置所有状态
    this.promises = []
    this.results = []
    this.errors = []
    this.completed = 0
    this.total = 0
    return this
  }

  /**
   * 获取当前批次状态
   * @description 返回批次的详细执行状态信息
   * @returns {Object} 状态信息对象
   * @returns {number} return.total - 总请求数
   * @returns {number} return.completed - 已完成请求数
   * @returns {number} return.pending - 待处理请求数
   * @returns {number} return.errors - 错误请求数
   * @returns {boolean} return.isExecuting - 是否正在执行
   * @returns {number} return.progress - 执行进度（0-1）
   * @example
   * // 监控批次执行状态
   * const status = batch.getStatus()
   * console.log(`进度: ${Math.round(status.progress * 100)}%`)
   * console.log(`完成: ${status.completed}/${status.total}`)
   * console.log(`错误: ${status.errors}`)
   */
  getStatus() {
    return {
      total: this.total,
      completed: this.completed,
      pending: this.total - this.completed,
      errors: this.errors.length,
      isExecuting: this.isExecuting,
      progress: this.total > 0 ? this.completed / this.total : 0
    }
  }
}

/**
 * 创建批量请求处理器实例的工厂函数
 * @description 便捷的工厂函数，用于创建BatchRequest实例
 * @param {Object} [options={}] - 配置选项，参数同BatchRequest构造函数
 * @returns {BatchRequest} 新的BatchRequest实例
 * @example
 * // 使用工厂函数创建批量请求处理器
 * const batch = createBatchRequest({
 *   concurrency: 3,
 *   timeout: 5000,
 *   retryCount: 2
 * })
 * 
 * // 等价于
 * const batch = new BatchRequest({
 *   concurrency: 3,
 *   timeout: 5000,
 *   retryCount: 2
 * })
 */
export const createBatchRequest = (options = {}) => {
  return new BatchRequest(options)
}