/**
 * @fileoverview 物流状态语义分析器
 * @description 根据物流轨迹信息智能分析当前物流状态，集成 compromise 库进行增强语义分析
 * <AUTHOR> Team
 * @version 1.0.0
 */

import nlp from 'compromise'

/**
 * 物流状态枚举常量
 * @readonly
 * @enum {string}
 */
export const LOGISTICS_STATUS = {
  /** 待揽件 */
  PENDING_PICKUP: 'pending_pickup',
  /** 已揽件 */
  PICKED_UP: 'picked_up',
  /** 运输中 */
  IN_TRANSIT: 'in_transit',
  /** 派送中 */
  OUT_FOR_DELIVERY: 'out_for_delivery',
  /** 已签收 */
  DELIVERED: 'delivered',
  /** 派送失败 */
  FAILED_DELIVERY: 'failed_delivery',
  /** 已退回 */
  RETURNED: 'returned',
  /** 异常 */
  EXCEPTION: 'exception'
}

/**
 * 物流状态显示文本映射
 * @readonly
 * @type {Object<string, string>}
 */
export const STATUS_TEXT_MAP = {
  [LOGISTICS_STATUS.PENDING_PICKUP]: '待揽件',
  [LOGISTICS_STATUS.PICKED_UP]: '已揽收',
  [LOGISTICS_STATUS.IN_TRANSIT]: '运输中',
  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: '派送中',
  [LOGISTICS_STATUS.DELIVERED]: '已签收',
  [LOGISTICS_STATUS.FAILED_DELIVERY]: '派送失败',
  [LOGISTICS_STATUS.RETURNED]: '已退回',
  [LOGISTICS_STATUS.EXCEPTION]: '异常'
}

/**
 * 物流状态关键词匹配规则配置
 * @description 按权重分级的关键词匹配规则，支持高、中、低三个权重等级
 * @readonly
 * @type {Object<string, Object<string, string[]>>}
 */
const STATUS_KEYWORDS = {
  [LOGISTICS_STATUS.PENDING_PICKUP]: {
    high: ['待揽收', '等待揽收', '待收件', '等待收件', '订单已提交', '已下单待揽收', '等待揽件', '待揽件', '等待取件人', '寄件人已下单', '等待快递员上门', '预约上门取件', '待取件'],
    medium: ['已下单', '订单生成', '等待取件', '预约取件', '订单受理', '订单处理中', '等待快递员', '等待上门', '预约上门', '准备取件', '安排取件'],
    low: ['订单信息', '寄件信息', '下单成功', '订单确认', '等待处理', '信息录入', '订单创建']
  },

  [LOGISTICS_STATUS.PICKED_UP]: {
    high: ['已揽收', '揽件成功', '收件成功', '已收件', '揽投员已收件', '快递员已取件', '揽件', '已揽件', '取件成功', '上门收件', '收寄成功', '成功揽收', '揽收完成'],
    medium: ['已取件', '已从.*收寄', '收寄完成', '揽投员', '已收取', '收件完成', '快递员收件', '业务员收件', '已收货', '收件员', '取件员', '收件人员', '揽收人员'],
    low: ['已从', '收寄', '取件', '收货', '收取', '揽收网点', '收件网点', '收件点', '取件点']
  },

  [LOGISTICS_STATUS.IN_TRANSIT]: {
    high: [
      '运输中', '在途中', '运输途中', '正在运输', '运输', '在途', '途中', '运送中', 
      '转运途中', '正在转运', '运输过程中', '已出库',
      // 中转相关 - 避免与签收冲突
      '已发往.*中转', '离开.*中转', '已到达.*分拨中心', '已离开.*分拨中心',
      '已到达.*转运中心', '已驶离.*转运中心', '到达目的地城市',
      // 明确排除签收相关的到达
      '已到达.*(?!.*签收).*中转', '已到达.*(?!.*签收).*分拨'
    ],
    medium: [
      '转运中', '中转', '发往', '离开', '到达.*中转', '经过', '途经', '装车发往', 
      '已装车', '发车', '运往', '送往', '转运', '中转处理', '分拣中', '正在分拣', 
      '分拨中', '干线运输', '航空运输', '交航', '航班起飞', '航班落地', 
      '清关中', '已清关', '待清关', '口岸放行', '口岸交接', '海关查验', '查验放行'
    ],
    low: [
      '中转站', '分拣中心', '装车', '发车', '运输', '转运', '分拣', '集散', 
      '处理中心', '转运中心', '集散中心', '分拣完成', '装卸', '中转处理', 
      '分拨中心', '转运仓', '口岸', '海关', '航班', '干线'
    ]
  },

  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: {
    high: ['派送中', '正在派送', '派件中', '配送中', '正在配送', '开始派送', '派送员.*派送', '出库派送', '安排派送', '派送', '配送', '正在投递', '投递中', '正在为您派送', '开始为您派送', '已到达派送站', '已到达派件网点'],
    medium: ['投递中', '快递员.*派送', '派送员', '配送员', '正在派件', '派送途中', '准备派送', '派件', '投递', '送件中', '配送途中', '正在投递', '派送人员', '投递人员', '即将派送', '今日派送', '安排快递员派送', '前往派送中'],
    low: ['最后一公里', '同城配送', '本地派送', '终端派送', '末端配送', '派送站', '配送站', '投递员', '派送网点', '配送网点', '派件网点', '派件站', '末端网点']
  },

  [LOGISTICS_STATUS.DELIVERED]: {
    high: [
      // 明确的签收表达
      '已签收', '签收成功', '签收完成', '完成签收', '成功签收', '本人签收', '客户签收', '收货签收',
      // 妥投相关
      '妥投', '妥投成功', '妥投完成', '成功妥投',
      // 投递完成
      '投递成功', '投递完成', '派送成功', '配送成功', '已送达', '送达成功',
      // 正则表达式匹配模式 - 提高权重
      '.*已由.*签收.*', '.*快件已由.*签收.*', '.*包裹已由.*签收.*', '.*已在.*签收.*',
      '.*签收.*如有疑问.*', '.*签收.*联系.*', '.*签收.*感谢.*',
      // 特定格式的签收表达（针对用户案例）
      '.*快件已由【.*】签收.*', '.*包裹已由【.*】签收.*', '.*已由【.*】签收.*',
      '您的.*已由.*签收.*', '您的快件已由.*签收.*', '您的包裹已由.*签收.*',
      // 常见签收地点格式
      '.*已由.*门口.*签收.*', '.*已由.*楼下.*签收.*', '.*已由.*前台.*签收.*',
      '.*已由.*保安.*签收.*', '.*已由.*物业.*签收.*', '.*已由.*家人.*签收.*',
      // 自提成功
      '自提成功', '取件成功', '快递柜取件成功', '驿站自提成功', '柜子取件成功',
      // 代收签收
      '代收成功', '代签成功', '已代收', '已代签收',
      // 其他明确完成状态
      '收件完成', '取货完成', '领取完成', '提取成功'
    ],
    medium: [
      '配送完成', '已投递', '代收', '他人代收', '成功投递', '投递完成', '签收人', '代签收', 
      '家人代收', '同事代收', '邻居代收', '代为签收', '委托签收', '门卫代收', '前台代收', 
      '物业代收', '已代签', '保安代收', '管理处代收', '收发室代收', '值班室代收',
      // 地点签收
      '门口签收', '楼下签收', '公司签收', '家中签收', '办公室签收', '宿舍签收',
      // 时间相关完成
      '今日已签收', '当日签收', '已于.*签收', '于.*完成签收'
    ],
    low: [
      '收件人', '快递柜', '驿站', '代签', '菜鸟驿站', '丰巢', '速递易', '近邻宝', 
      '收件', '取件', '自提', '智能柜', '自助柜', '代收点', '服务点', '便民点'
    ]
  },

  [LOGISTICS_STATUS.FAILED_DELIVERY]: {
    high: ['派送失败', '投递失败', '配送失败', '无人签收', '拒收', '客户拒收', '派送不成功', '投递不成功', '无法派送', '派送异常', '投递异常', '配送异常', '妥投失败', '地址无效', '超区不派', '超区'],
    medium: ['地址错误', '联系不上', '电话无人接听', '改派', '延误派送', '无法联系', '地址不详', '收件人不在', '无人接听', '联系失败', '改期配送', '地址有误', '信息有误', '电话空号', '停机', '多次联系未果', '天气原因延误', '客户要求改期'],
    low: ['暂存', '改期派送', '预约派送', '地址有误', '电话错误', '信息不全', '待联系', '重新派送', '二次派送', '改期投递', '暂无法派送', '待二次派送']
  },

  [LOGISTICS_STATUS.RETURNED]: {
    high: ['已退回', '退回发件人', '原路返回', '逆向物流', '退件', '退回', '返回发件人', '退货', '原件退回', '退回寄件人', '退回原地', '退回途中', '已退仓', '已逆向入库', '准备退回', '退回揽收网点'],
    medium: ['退回', '返回', '退货', '回退', '退回寄件人', '返回原地', '逆向运输', '退回处理', '原路退回', '逆向配送', '超时未取退回', '拒收退回', '驿站超时未取退回'],
    low: ['返回途中', '退回中', '退回运输', '逆向', '回程', '退回流程', '逆向流程', '逆向入库', '退回处理中']
  },

  [LOGISTICS_STATUS.EXCEPTION]: {
    high: ['异常', '问题件', '滞留', '丢失', '破损', '包裹异常', '运输异常', '快件异常', '处理异常', '派送异常', '投递异常', '系统异常', '网络异常', '爆仓', '暴雨停运', '天气原因', '道路管制', '疫情管控', '安检不通过', '违禁品', '少件', '漏件', '超长超重', '异常签收', '拦截', '滞留件'],
    medium: ['超时', '延误', '无法派送', '地址不详', '收件人信息有误', '信息错误', '联系异常', '处理失败', '系统异常', '网络异常', '服务异常', '操作异常', '物流拦截', '安检异常', '包裹破损风险', '地址偏远'],
    low: ['暂扣', '待处理', '需核实', '待确认', '核实中', '查询中', '调查中', '待查', '核查中', '风险件', '需补充资料']
  }
}

/**
 * 分析物流状态
 * @description 根据物流轨迹信息智能分析当前物流状态，支持单条记录分析和全轨迹历史分析
 * @param {Array<Object>} orderTrack - 物流轨迹数组，按时间倒序排列
 * @param {string} orderTrack[].context - 轨迹描述文本
 * @param {string} orderTrack[].content - 轨迹内容（备用字段）
 * @param {string} orderTrack[].msgTime - 轨迹时间戳
 * @param {string} orderTrack[].time - 轨迹时间（备用字段）
 * @returns {Object} 物流状态分析结果
 * @returns {string} returns.status - 分析得出的物流状态
 * @returns {string} returns.statusText - 状态显示文本
 * @returns {Object} returns.latestTrack - 最新轨迹记录
 * @returns {number} returns.confidence - 分析置信度 (0-1)
 * @returns {string} returns.analysisMethod - 分析方法标识
 * @example
 * const result = analyzeLogisticsStatus([
 *   { context: '快件已由张三签收', msgTime: '2023-12-01 10:00:00' },
 *   { context: '正在派送中', msgTime: '2023-12-01 09:00:00' }
 * ])
 * console.log(result.status) // 'delivered'
 * console.log(result.confidence) // 0.95
 */
export function analyzeLogisticsStatus(orderTrack) {
  if (!orderTrack || orderTrack.length === 0) {
    return {
      status: LOGISTICS_STATUS.PENDING_PICKUP,
      statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
      latestTrack: null,
      confidence: 0,
      analysisMethod: 'default'
    }
  }

  // 获取最新的轨迹记录
  const latestTrack = orderTrack[0]
  const context = latestTrack.context || latestTrack.content || ''

  // 优化策略：优先分析最新记录，如果置信度不够则分析整个轨迹
  const latestAnalysis = analyzeTrackContext(context, latestTrack)
  // 如果最新记录分析置信度足够高且不是模糊状态，直接返回结果
  if (latestAnalysis.confidence >= 0.8 && !isAmbiguousContext(context)) {
    return {
      ...latestAnalysis,
      latestTrack,
      analysisMethod: 'latest_record_high_confidence'
    }
  }

  // 如果最新记录置信度不够或者是模糊状态，进行全轨迹分析
  const fullTrackAnalysis = analyzeFullTrackHistory(orderTrack)

  // 比较两种分析结果，选择更可靠的
  const finalResult = selectBestAnalysis(latestAnalysis, fullTrackAnalysis)

  return {
    ...finalResult,
    latestTrack,
    latestAnalysis,
    fullTrackAnalysis
  }
}

/**
 * 分析单条轨迹记录的上下文
 * @description 对单条物流轨迹记录进行语义分析，结合关键词匹配、语义增强和时间因素
 * @param {string} context - 轨迹描述文本
 * @param {Object} trackRecord - 轨迹记录对象
 * @param {string} trackRecord.msgTime - 轨迹时间戳
 * @param {string} trackRecord.time - 轨迹时间（备用字段）
 * @returns {Object} 轨迹分析结果
 * @returns {string} returns.status - 分析得出的物流状态
 * @returns {string} returns.statusText - 状态显示文本
 * @returns {number} returns.confidence - 分析置信度 (0-1)
 * @returns {Object} returns.details - 详细分析信息
 * @returns {number} returns.details.keywordScore - 关键词匹配得分
 * @returns {number} returns.details.semanticBonus - 语义增强得分
 * @returns {number} returns.details.timeBonus - 时间因素得分
 * @example
 * const result = analyzeTrackContext('快件已由张三签收', {
 *   msgTime: '2023-12-01 10:00:00'
 * })
 * console.log(result.status) // 'delivered'
 * console.log(result.confidence) // 0.95
 */
function analyzeTrackContext(context, trackRecord = {}) {
  // 验证输入参数
  if (!context) {
    return {
      status: LOGISTICS_STATUS.EXCEPTION,
      statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.EXCEPTION],
      confidence: 0,
      details: { error: '无效的轨迹信息' }
    }
  }

  let maxScore = 0
  let detectedStatus = LOGISTICS_STATUS.PENDING_PICKUP
  let statusScores = {}

  // 遍历所有状态的关键词
  for (const [status, keywords] of Object.entries(STATUS_KEYWORDS)) {
    const score = calculateKeywordScore(context, keywords, status)
    statusScores[status] = score

    if (score > maxScore) {
      maxScore = score
      detectedStatus = status
    }
  }

  // 先保存原始关键词匹配结果
  const keywordMatchResult = {
    status: detectedStatus,
    score: maxScore,
    confidence: Math.min(maxScore / 50, 1)
  }

  // 语义增强分析
  const semanticResult = enhancedSemanticAnalysis(context)

  // 时间因素分析
  const timeAnalysis = analyzeTimeFactors(trackRecord)

  // 语义分析加成
  maxScore += semanticResult.semanticScore

  // 时间因素加成
  maxScore += timeAnalysis.timeBonus

  // 使用 compromise 分析结果进行状态修正
  const compromiseStatus = predictStatusFromCompromise(semanticResult.compromiseAnalysis, context)
  if (compromiseStatus && compromiseStatus.confidence > 0.7) {
    // 如果关键词匹配得分很高（超过25分），优先相信关键词匹配
    if (keywordMatchResult.score >= 25) {
      maxScore += 5
    } else {
      // 关键词匹配得分不高时，考虑 compromise 建议
      if (compromiseStatus.status !== detectedStatus) {
        const currentConfidence = Math.min(maxScore / 50, 1)
        if (compromiseStatus.confidence > currentConfidence) {
          detectedStatus = compromiseStatus.status
          maxScore = compromiseStatus.confidence * 50
        }
      } else {
        // 状态一致时，提升置信度
        maxScore += 8
      }
    }
  }

  // 计算最终置信度
  let confidence = Math.min(maxScore / 50, 1)
  confidence = Math.max(confidence, 0.1)

  return {
    status: detectedStatus,
    statusText: STATUS_TEXT_MAP[detectedStatus],
    confidence,
    rawScore: maxScore,
    keywordScores: statusScores,
    semanticAnalysis: semanticResult,
    timeAnalysis,
    analysisMethod: 'single_track'
  }
}

/**
 * 分析完整的轨迹历史
 * @description 对完整的物流轨迹历史进行综合分析，考虑状态一致性和转换有效性
 * @param {Array<Object>} orderTrack - 完整的轨迹数组，按时间倒序排列
 * @param {string} orderTrack[].context - 轨迹描述文本
 * @param {string} orderTrack[].msgTime - 轨迹时间戳
 * @returns {Object} 全轨迹分析结果
 * @returns {string} returns.status - 分析得出的物流状态
 * @returns {string} returns.statusText - 状态显示文本
 * @returns {number} returns.confidence - 分析置信度 (0-1)
 * @returns {Object} returns.details - 详细分析信息
 * @returns {number} returns.details.consistencyScore - 状态一致性得分
 * @returns {number} returns.details.transitionValidityRatio - 状态转换有效性比例
 * @returns {string} returns.analysisMethod - 分析方法标识
 * @example
 * const result = analyzeFullTrackHistory([
 *   { context: '快件已由张三签收', msgTime: '2023-12-01 10:00:00' },
 *   { context: '正在派送中', msgTime: '2023-12-01 09:00:00' },
 *   { context: '到达派送网点', msgTime: '2023-12-01 08:00:00' }
 * ])
 * console.log(result.status) // 'delivered'
 * console.log(result.confidence) // 0.92
 */
function analyzeFullTrackHistory(orderTrack) {
  if (!orderTrack || orderTrack.length === 0) {
    return {
      status: LOGISTICS_STATUS.PENDING_PICKUP,
      statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
      confidence: 0,
      analysisMethod: 'full_track_empty'
    }
  }

  // 分析状态转换序列
  const statusSequence = []
  const trackAnalyses = []

  // 分析每条轨迹记录
  for (let i = 0; i < Math.min(orderTrack.length, 5); i++) {
    const track = orderTrack[i]
    const context = track.context || track.content || ''
    const analysis = analyzeTrackContext(context, track)

    trackAnalyses.push({
      ...analysis,
      trackIndex: i,
      timestamp: track.msgTime || track.time,
      isLatest: i === 0
    })

    statusSequence.push(analysis.status)
  }

  // 状态一致性检查
  const consistencyAnalysis = analyzeStatusConsistency(statusSequence)

  // 状态转换逻辑验证
  const transitionAnalysis = validateStatusTransitions(statusSequence)

  // 综合分析得出最终状态
  const finalStatus = determineFinalStatus(trackAnalyses, consistencyAnalysis, transitionAnalysis)

  // 如果最新记录很模糊，尝试从历史轨迹推断当前状态
  if (trackAnalyses.length > 1 && trackAnalyses[0].confidence < 0.5) {
    const inferredStatus = inferStatusFromHistory(trackAnalyses)
    if (inferredStatus && inferredStatus.confidence > finalStatus.confidence) {
      return {
        ...inferredStatus,
        statusSequence,
        trackAnalyses,
        consistencyAnalysis,
        transitionAnalysis,
        analysisMethod: 'inferred_from_history'
      }
    }
  }

  return {
    ...finalStatus,
    statusSequence,
    trackAnalyses,
    consistencyAnalysis,
    transitionAnalysis,
    analysisMethod: 'full_track_analysis'
  }
}

/**
 * 选择最佳分析结果
 * @description 比较最新记录分析和全轨迹分析结果，选择置信度更高的结果
 * @param {Object} latestAnalysis - 最新记录分析结果
 * @param {string} latestAnalysis.status - 最新记录分析状态
 * @param {number} latestAnalysis.confidence - 最新记录分析置信度
 * @param {Object} fullTrackAnalysis - 全轨迹分析结果
 * @param {string} fullTrackAnalysis.status - 全轨迹分析状态
 * @param {number} fullTrackAnalysis.confidence - 全轨迹分析置信度
 * @returns {Object} 最佳分析结果
 * @returns {string} returns.status - 选择的最佳状态
 * @returns {number} returns.confidence - 选择的最佳置信度
 * @returns {string} returns.analysisMethod - 选择的分析方法
 * @example
 * const best = selectBestAnalysis(
 *   { status: 'delivered', confidence: 0.7 },
 *   { status: 'delivered', confidence: 0.9 }
 * )
 * console.log(best.confidence) // 0.9
 */
function selectBestAnalysis(latestAnalysis, fullTrackAnalysis) {
  // 如果最新记录置信度很高且是终态，优先选择
  if (latestAnalysis.confidence >= 0.8 && isFinalStatus(latestAnalysis.status)) {
    return {
      ...latestAnalysis,
      analysisMethod: 'latest_high_confidence_final'
    }
  }

  // 如果全轨迹分析置信度更高，选择全轨迹结果
  if (fullTrackAnalysis.confidence > latestAnalysis.confidence + 0.2) {
    return {
      ...fullTrackAnalysis,
      analysisMethod: 'full_track_higher_confidence'
    }
  }

  // 如果状态一致且都有合理置信度，选择置信度更高的
  if (latestAnalysis.status === fullTrackAnalysis.status) {
    const bestAnalysis = latestAnalysis.confidence >= fullTrackAnalysis.confidence
      ? latestAnalysis
      : fullTrackAnalysis

    return {
      ...bestAnalysis,
      confidence: Math.max(latestAnalysis.confidence, fullTrackAnalysis.confidence),
      analysisMethod: 'consistent_status_best_confidence'
    }
  }

  // 默认选择最新记录分析，但降低置信度
  return {
    ...latestAnalysis,
    confidence: Math.max(latestAnalysis.confidence * 0.8, 0.1),
    analysisMethod: 'latest_with_reduced_confidence'
  }
}

/**
 * 基于 compromise 语义分析预测状态
 * @description 使用 compromise 自然语言处理库进行语义分析，预测物流状态
 * @param {Object} compromiseAnalysis - compromise 分析结果
 * @param {string} context - 轨迹描述文本
 * @returns {Object|null} 预测的物流状态或null（如果无法预测）
 * @example
 * const status = predictStatusFromCompromise(analysis, '包裹已经送达客户手中')
 * console.log(status.status) // 'delivered'
 * 
 * const status2 = predictStatusFromCompromise(analysis, '正在运输途中')
 * console.log(status2.status) // 'in_transit'
 */
function predictStatusFromCompromise(compromiseAnalysis, context) {
  if (!compromiseAnalysis || compromiseAnalysis.semanticBonus === 0) {
    return null
  }

  const { verbs, tenseAnalysis, sentiment, places } = compromiseAnalysis
  let predictedStatus = null
  let confidence = 0.5

  // 基于动词预测状态
  const verbLower = verbs.map(v => v.toLowerCase())

  if (verbLower.some(v => ['delivered', 'signed', 'completed', 'received'].includes(v))) {
    predictedStatus = LOGISTICS_STATUS.DELIVERED
    confidence = 0.9
  } else if (verbLower.some(v => ['delivering', 'dispatching', 'sending'].includes(v))) {
    predictedStatus = LOGISTICS_STATUS.OUT_FOR_DELIVERY
    confidence = 0.8
  } else if (verbLower.some(v => ['picked', 'collected', 'received'].includes(v)) && tenseAnalysis.isPast) {
    predictedStatus = LOGISTICS_STATUS.PICKED_UP
    confidence = 0.8
  } else if (verbLower.some(v => ['moving', 'traveling', 'transporting'].includes(v))) {
    predictedStatus = LOGISTICS_STATUS.IN_TRANSIT
    confidence = 0.7
  } else if (verbLower.some(v => ['failed', 'rejected', 'unable'].includes(v))) {
    predictedStatus = LOGISTICS_STATUS.FAILED_DELIVERY
    confidence = 0.8
  }

  // 基于时态调整预测
  if (tenseAnalysis.isPast && sentiment.polarity > 0) {
    // 过去时态 + 正面情感 = 可能已完成
    if (!predictedStatus) {
      predictedStatus = LOGISTICS_STATUS.DELIVERED
      confidence = 0.6
    } else if (predictedStatus === LOGISTICS_STATUS.DELIVERED) {
      confidence = Math.min(confidence + 0.1, 1)
    }
  } else if (tenseAnalysis.isProgressive) {
    // 进行时态 = 正在进行的动作
    if (!predictedStatus) {
      predictedStatus = LOGISTICS_STATUS.IN_TRANSIT
      confidence = 0.6
    }
  }

  // 基于地点信息调整
  if (places.length > 0) {
    confidence = Math.min(confidence + 0.1, 1)
  }

  // 基于情感极性调整
  if (sentiment.polarity < -1) {
    // 强烈负面情感可能表示问题
    if (predictedStatus === LOGISTICS_STATUS.OUT_FOR_DELIVERY) {
      predictedStatus = LOGISTICS_STATUS.FAILED_DELIVERY
    } else if (!predictedStatus) {
      predictedStatus = LOGISTICS_STATUS.EXCEPTION
      confidence = 0.7
    }
  }

  return predictedStatus ? {
    status: predictedStatus,
    confidence,
    source: 'compromise'
  } : null
}

/**
 * 计算关键词匹配得分
 * @description 根据关键词权重配置计算文本匹配得分，支持动态权重和正则表达式匹配
 * @param {string} text - 待分析的文本内容
 * @param {Object} keywords - 关键词配置对象
 * @param {string[]} keywords.high - 高权重关键词数组
 * @param {string[]} keywords.medium - 中权重关键词数组
 * @param {string[]} keywords.low - 低权重关键词数组
 * @param {string} statusType - 状态类型，用于特殊权重处理
 * @returns {number} 关键词匹配得分 (0-1)
 * @example
 * const score = calculateKeywordScore('快件已签收', {
 *   high: ['已签收', '签收'],
 *   medium: ['收到', '送达'],
 *   low: ['派送']
 * })
 * console.log(score) // 0.9
 */
function calculateKeywordScore(text, keywords, statusType = '') {
  let score = 0
  const lowerText = text.toLowerCase()
  const textLength = text.length
  const matchedKeywords = [] // 用于调试

  // 动态权重配置 - 根据文本长度调整
  const baseWeights = { high: 30, medium: 15, low: 8 }
  const lengthFactor = Math.min(textLength / 20, 2) // 文本越长，权重略微降低
  const weights = {
    high: Math.round(baseWeights.high / lengthFactor),
    medium: Math.round(baseWeights.medium / lengthFactor),
    low: Math.round(baseWeights.low / lengthFactor)
  }

  // 已签收状态特殊权重加成
  const isDeliveredStatus = statusType === LOGISTICS_STATUS.DELIVERED
  if (isDeliveredStatus) {
    weights.high = Math.round(weights.high * 1.5) // 已签收状态高权重关键词额外加成50%
    weights.medium = Math.round(weights.medium * 1.3) // 中权重关键词额外加成30%
  }

  for (const [level, keywordList] of Object.entries(keywords)) {
    const weight = weights[level] || 8

    for (const keyword of keywordList) {
      const lowerKeyword = keyword.toLowerCase()
      let matchScore = 0

      // 支持正则表达式匹配
      if (keyword.includes('.*')) {
        try {
          const regex = new RegExp(lowerKeyword, 'i')
          if (regex.test(lowerText)) {
            matchScore = weight * 1.5 // 正则匹配额外加分
            matchedKeywords.push({ keyword, level, score: matchScore, type: 'regex' })
          }
        } catch (e) {
          // 正则表达式错误时降级为普通匹配
          const fallbackKeyword = lowerKeyword.replace(/\.\*/g, '')
          if (lowerText.includes(fallbackKeyword)) {
            matchScore = weight
            matchedKeywords.push({ keyword: fallbackKeyword, level, score: matchScore, type: 'fallback' })
          }
        }
      } else {
        // 智能关键词匹配
        if (lowerText.includes(lowerKeyword)) {
          // 计算匹配质量
          const matchQuality = calculateMatchQuality(lowerText, lowerKeyword)
          matchScore = weight * matchQuality
          
          // 签收关键词特殊加成
          if (isDeliveredStatus && (lowerKeyword.includes('签收') || lowerKeyword.includes('妥投'))) {
            matchScore *= 1.8 // 签收相关关键词额外80%加成
          }
          
          matchedKeywords.push({ keyword: lowerKeyword, level, score: matchScore, type: 'normal', quality: matchQuality })
        }
      }

      // 避免重复计分（同一个关键词在文本中多次出现）
      if (matchScore > 0) {
        const occurrences = (lowerText.match(new RegExp(lowerKeyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')) || []).length
        const occurrenceBonus = Math.min(occurrences * 0.2, 1) // 最多20%的额外加分
        score += matchScore * (1 + occurrenceBonus)
      }
    }
  }

  // 调试日志
  if (matchedKeywords.length > 0) {
    console.log(`[物流状态分析] 文本: "${text}" | 状态: ${statusType} | 匹配关键词:`, matchedKeywords, `| 总分: ${Math.round(score)}`)
  }

  return Math.round(score)
}

/**
 * 计算关键词匹配质量
 * @description 计算关键词在文本中的匹配质量，考虑位置、长度等因素
 * @param {string} text - 待分析的文本内容
 * @param {string} keyword - 匹配的关键词
 * @returns {number} 匹配质量系数 (0.5-2.0)
 * @example
 * const quality = calculateMatchQuality('快件已签收完成', '已签收')
 * console.log(quality) // 1.5
 */
function calculateMatchQuality(text, keyword) {
  // 完全匹配
  if (text.trim() === keyword) {
    return 2.0
  }

  // 独立词匹配（前后有空格或标点）
  const wordBoundaryRegex = new RegExp(`\\b${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i')
  if (wordBoundaryRegex.test(text)) {
    return 1.5
  }

  // 开头匹配
  if (text.startsWith(keyword)) {
    return 1.3
  }

  // 结尾匹配
  if (text.endsWith(keyword)) {
    return 1.2
  }

  // 普通包含匹配
  return 1.0
}

/**
 * 检查是否为模糊上下文
 * @description 检测轨迹文本是否包含模糊或不确定的表述
 * @param {string} context - 轨迹描述文本
 * @returns {boolean} 如果文本模糊则返回true，否则返回false
 * @example
 * const isAmbiguous = isAmbiguousContext('可能已经送达')
 * console.log(isAmbiguous) // true
 * 
 * const isAmbiguous2 = isAmbiguousContext('快件已签收')
 * console.log(isAmbiguous2) // false
 */
function isAmbiguousContext(context) {
  const ambiguousKeywords = [
    '处理中', '处理', '操作中', '进行中', '执行中',
    '快件', '包裹', '订单', '货物', '商品',
    '正在', '进行', '执行', '操作'
  ]

  const lowerContext = context.toLowerCase()

  // 如果文本很短且只包含模糊词汇，认为是模糊的
  if (context.length < 10) {
    return ambiguousKeywords.some(keyword => lowerContext.includes(keyword))
  }

  // 如果文本中模糊词汇占比过高，认为是模糊的
  const ambiguousCount = ambiguousKeywords.filter(keyword =>
    lowerContext.includes(keyword)
  ).length

  return ambiguousCount >= 2 && context.length < 20
}

/**
 * 从历史轨迹推断当前状态
 * @description 基于历史轨迹分析结果推断当前可能的物流状态
 * @param {Array<Object>} trackAnalyses - 轨迹分析结果数组
 * @param {string} trackAnalyses[].status - 轨迹分析状态
 * @param {number} trackAnalyses[].confidence - 轨迹分析置信度
 * @returns {Object|null} 推断结果或null（如果无法推断）
 * @returns {string} returns.status - 推断的物流状态
 * @returns {string} returns.statusText - 状态显示文本
 * @returns {number} returns.confidence - 推断置信度
 * @example
 * const result = inferStatusFromHistory([
 *   { status: 'out_for_delivery', confidence: 0.8 },
 *   { status: 'in_transit', confidence: 0.9 }
 * ])
 * console.log(result.status) // 'delivered'
 */
function inferStatusFromHistory(trackAnalyses) {
  if (trackAnalyses.length < 2) return null

  // 获取最近几条有意义的轨迹
  const meaningfulTracks = trackAnalyses.filter(track => track.confidence > 0.3)

  if (meaningfulTracks.length === 0) return null

  // 找到最近的高置信度状态
  const lastHighConfidenceTrack = meaningfulTracks.find(track => track.confidence >= 0.7)

  if (!lastHighConfidenceTrack) return null

  // 基于最后的高置信度状态推断当前可能的状态
  const lastStatus = lastHighConfidenceTrack.status
  const possibleNextStates = getPossibleNextStates(lastStatus)

  // 如果只有一个可能的下一状态，使用它
  if (possibleNextStates.length === 1) {
    return {
      status: possibleNextStates[0],
      statusText: STATUS_TEXT_MAP[possibleNextStates[0]],
      confidence: Math.min(lastHighConfidenceTrack.confidence * 0.8, 0.7)
    }
  }

  // 如果有多个可能状态，选择最合理的
  const mostLikelyStatus = selectMostLikelyNextStatus(lastStatus, trackAnalyses)

  return mostLikelyStatus ? {
    status: mostLikelyStatus,
    statusText: STATUS_TEXT_MAP[mostLikelyStatus],
    confidence: Math.min(lastHighConfidenceTrack.confidence * 0.7, 0.6)
  } : null
}

/**
 * 获取可能的下一状态
 * @description 根据当前物流状态获取所有可能的下一个状态
 * @param {string} currentStatus - 当前物流状态
 * @returns {Array<string>} 可能的下一状态数组
 * @example
 * const nextStates = getPossibleNextStates('out_for_delivery')
 * console.log(nextStates) // ['delivered', 'failed_delivery']
 * 
 * const nextStates2 = getPossibleNextStates('delivered')
 * console.log(nextStates2) // [] (终态，无下一状态)
 */
function getPossibleNextStates(currentStatus) {
  const nextStatesMap = {
    [LOGISTICS_STATUS.PENDING_PICKUP]: [LOGISTICS_STATUS.PICKED_UP],
    [LOGISTICS_STATUS.PICKED_UP]: [LOGISTICS_STATUS.IN_TRANSIT],
    [LOGISTICS_STATUS.IN_TRANSIT]: [LOGISTICS_STATUS.OUT_FOR_DELIVERY],
    [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: [LOGISTICS_STATUS.DELIVERED, LOGISTICS_STATUS.FAILED_DELIVERY],
    [LOGISTICS_STATUS.FAILED_DELIVERY]: [LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.RETURNED],
    [LOGISTICS_STATUS.DELIVERED]: [], // 终态
    [LOGISTICS_STATUS.RETURNED]: [], // 终态
    [LOGISTICS_STATUS.EXCEPTION]: [LOGISTICS_STATUS.IN_TRANSIT, LOGISTICS_STATUS.OUT_FOR_DELIVERY]
  }

  return nextStatesMap[currentStatus] || []
}

/**
 * 选择最可能的下一状态
 * @description 基于最后的明确状态和轨迹分析结果，选择最可能的下一个状态
 * @param {string} lastStatus - 最后的明确物流状态
 * @param {Array<Object>} trackAnalyses - 轨迹分析结果数组
 * @returns {string|null} 最可能的下一状态或null
 * @example
 * const nextStatus = selectMostLikelyNextStatus('out_for_delivery', trackAnalyses)
 * console.log(nextStatus) // 'delivered' (派送中更可能变成已签收)
 * 
 * const nextStatus2 = selectMostLikelyNextStatus('failed_delivery', trackAnalyses)
 * console.log(nextStatus2) // 'out_for_delivery' (失败后更可能重新派送)
 */
function selectMostLikelyNextStatus(lastStatus, trackAnalyses) {
  const possibleStates = getPossibleNextStates(lastStatus)

  if (possibleStates.length === 0) return lastStatus
  if (possibleStates.length === 1) return possibleStates[0]

  // 基于时间和常见流程选择
  if (lastStatus === LOGISTICS_STATUS.OUT_FOR_DELIVERY) {
    // 派送中状态更可能变成已签收而不是失败
    return LOGISTICS_STATUS.DELIVERED
  }

  if (lastStatus === LOGISTICS_STATUS.FAILED_DELIVERY) {
    // 失败后更可能重新派送
    return LOGISTICS_STATUS.OUT_FOR_DELIVERY
  }

  // 默认返回第一个可能状态
  return possibleStates[0]
}

/**
 * 上下文语义增强分析
 * @description 集成 compromise 库进行深度语义分析，包括模式匹配、语义强度分析和上下文连贯性分析
 * @param {string} context - 轨迹描述文本
 * @returns {Object} 语义分析结果
 * @returns {number} returns.semanticScore - 语义得分
 * @returns {number} returns.coherence - 上下文连贯性得分
 * @returns {string} returns.intensity - 语义强度 ('high' | 'normal')
 * @returns {Object} returns.compromiseAnalysis - compromise库分析结果
 * @example
 * const result = enhancedSemanticAnalysis('快件已由张三签收')
 * console.log(result.semanticScore) // 20 (检测到签收模式)
 * console.log(result.intensity) // 'high'
 */
function enhancedSemanticAnalysis(context) {
  // 基础模式匹配
  const negativePatterns = [
    '未.*成功', '尚未.*', '暂未.*', '等待.*', '准备.*', '即将.*'
  ]

  const positivePatterns = [
    '已.*完成', '成功.*', '顺利.*', '正常.*'
  ]

  // 新增：语义强度分析
  const strongPositive = ['妥投', '签收成功', '投递完成', '已签收', '签收完成']
  const strongNegative = ['派送失败', '拒收', '异常', '丢失']
  
  // 特殊签收模式（针对用户案例）
  const deliveryPatterns = [
    '已由.*签收', '快件已由.*签收', '包裹已由.*签收', 
    '您的.*已由.*签收', '.*已由【.*】签收.*'
  ]

  let semanticScore = 0
  const lowerContext = context.toLowerCase()

  // 使用 compromise 进行语义分析
  const compromiseResult = analyzeWithCompromise(context)

  // 特殊处理：签收模式检测（最高优先级）
  for (const pattern of deliveryPatterns) {
    const regex = new RegExp(pattern, 'i')
    if (regex.test(context)) {
      semanticScore += 20 // 给予最高语义分数
      console.log(`[语义分析] 检测到签收模式: "${pattern}" 在文本: "${context}"`)
      break // 只匹配一次，避免重复加分
    }
  }

  // 检查强烈肯定词汇
  for (const pattern of strongPositive) {
    if (context.includes(pattern)) {
      semanticScore += 10
    }
  }

  // 检查强烈否定词汇
  for (const pattern of strongNegative) {
    if (context.includes(pattern)) {
      semanticScore -= 10
    }
  }

  // 检查否定词汇
  for (const pattern of negativePatterns) {
    const regex = new RegExp(pattern, 'i')
    if (regex.test(context)) {
      semanticScore -= 5
    }
  }

  // 检查肯定词汇
  for (const pattern of positivePatterns) {
    const regex = new RegExp(pattern, 'i')
    if (regex.test(context)) {
      semanticScore += 5
    }
  }

  // 融合 compromise 分析结果
  semanticScore += compromiseResult.semanticBonus

  // 新增：上下文连贯性分析
  const contextCoherence = analyzeContextCoherence(context)
  semanticScore += contextCoherence

  return {
    semanticScore,
    coherence: contextCoherence,
    intensity: Math.abs(semanticScore) > 8 ? 'high' : 'normal',
    compromiseAnalysis: compromiseResult
  }
}

/**
 * 使用 compromise 库进行语义分析
 * @description 利用 compromise 自然语言处理库提取动词、名词、地点、人物等语义信息
 * @param {string} context - 轨迹描述文本
 * @returns {Object} compromise 分析结果
 * @returns {Array<string>} returns.verbs - 提取的动词数组
 * @returns {Array<string>} returns.nouns - 提取的名词数组
 * @returns {Array<string>} returns.places - 提取的地点数组
 * @returns {Array<string>} returns.people - 提取的人物数组
 * @returns {Object} returns.sentiment - 情感分析结果
 * @returns {Object} returns.tense - 时态分析结果
 * @returns {number} returns.semanticBonus - 语义增强得分
 * @example
 * const result = analyzeWithCompromise('快件已由张三在北京签收')
 * console.log(result.verbs) // ['签收']
 * console.log(result.people) // ['张三']
 * console.log(result.places) // ['北京']
 */
function analyzeWithCompromise(context) {
  try {
    const doc = nlp(context)

    // 提取动词和时态信息
    const verbs = doc.verbs().out('array')
    const pastTense = doc.verbs().toPastTense().out('array')
    const presentTense = doc.verbs().toPresentTense().out('array')

    // 提取名词（地点、人员等）
    const nouns = doc.nouns().out('array')
    const places = doc.places().out('array')
    const people = doc.people().out('array')

    // 情感分析
    const sentiment = analyzeSentimentWithCompromise(doc)

    // 时态分析
    const tenseAnalysis = analyzeTensePattern(doc)

    // 计算语义加成分数
    let semanticBonus = 0

    // 动词完成度分析
    if (verbs.some(verb => ['delivered', 'completed', 'finished', 'signed'].includes(verb.toLowerCase()))) {
      semanticBonus += 8
    }

    // 进行时态加成
    if (tenseAnalysis.isProgressive) {
      semanticBonus += 5
    }

    // 过去时态加成（表示已完成）
    if (tenseAnalysis.isPast) {
      semanticBonus += 6
    }

    // 地点信息加成
    if (places.length > 0) {
      semanticBonus += 3
    }

    // 人员信息加成
    if (people.length > 0) {
      semanticBonus += 2
    }

    // 情感极性加成
    semanticBonus += sentiment.polarity * 3

    return {
      verbs,
      nouns,
      places,
      people,
      sentiment,
      tenseAnalysis,
      semanticBonus: Math.max(-10, Math.min(10, semanticBonus)) // 限制范围
    }
  } catch (error) {
    console.warn('Compromise analysis failed:', error)
    return {
      verbs: [],
      nouns: [],
      places: [],
      people: [],
      sentiment: { polarity: 0, confidence: 0 },
      tenseAnalysis: { isPast: false, isProgressive: false },
      semanticBonus: 0
    }
  }
}

/**
 * 使用 compromise 进行情感分析
 * @description 基于预定义的正面和负面词汇列表进行情感分析
 * @param {Object} doc - compromise 文档对象
 * @returns {Object} 情感分析结果
 * @returns {number} returns.positive - 正面词汇计数
 * @returns {number} returns.negative - 负面词汇计数
 * @returns {number} returns.polarity - 情感极性 (正面-负面)
 * @returns {number} returns.confidence - 情感分析置信度
 * @example
 * const sentiment = analyzeSentimentWithCompromise(doc)
 * console.log(sentiment.polarity) // 2 (正面情感)
 * console.log(sentiment.confidence) // 0.8
 */
function analyzeSentimentWithCompromise(doc) {
  // 提取形容词进行情感分析
  const adjectives = doc.adjectives().out('array')

  // 定义情感词汇
  const positiveWords = ['successful', 'completed', 'delivered', 'good', 'normal', '成功', '完成', '正常', '顺利']
  const negativeWords = ['failed', 'error', 'problem', 'delayed', 'unable', '失败', '错误', '延误', '异常']

  let positiveScore = 0
  let negativeScore = 0

  // 分析形容词情感倾向
  adjectives.forEach(adj => {
    const lowerAdj = adj.toLowerCase()
    if (positiveWords.some(word => lowerAdj.includes(word))) {
      positiveScore++
    }
    if (negativeWords.some(word => lowerAdj.includes(word))) {
      negativeScore++
    }
  })

  // 分析整体文本情感
  const text = doc.out('text').toLowerCase()
  positiveWords.forEach(word => {
    if (text.includes(word)) positiveScore++
  })
  negativeWords.forEach(word => {
    if (text.includes(word)) negativeScore++
  })

  const polarity = positiveScore - negativeScore
  const confidence = Math.abs(polarity) / Math.max(positiveScore + negativeScore, 1)

  return {
    positive: positiveScore,
    negative: negativeScore,
    polarity,
    confidence
  }
}

/**
 * 分析时态模式
 * @description 分析文本中的动词时态，包括过去时、进行时和完成时
 * @param {Object} doc - compromise 文档对象
 * @returns {Object} 时态分析结果
 * @returns {boolean} returns.isPast - 是否包含过去时态
 * @returns {boolean} returns.isProgressive - 是否包含进行时态
 * @returns {boolean} returns.isPerfect - 是否包含完成时态
 * @returns {string} returns.dominantTense - 主导时态 ('past' | 'progressive' | 'present')
 * @example
 * const tense = analyzeTensePattern(doc)
 * console.log(tense.isPast) // true
 * console.log(tense.dominantTense) // 'past'
 */
function analyzeTensePattern(doc) {
  const verbs = doc.verbs()

  // 检查是否有过去时态
  const pastVerbs = verbs.toPastTense().out('array')
  const isPast = pastVerbs.length > 0

  // 检查是否有进行时态
  const progressiveVerbs = verbs.toGerund().out('array')
  const isProgressive = progressiveVerbs.length > 0 || doc.has('#Gerund')

  // 检查是否有完成时态
  const perfectVerbs = verbs.conjugate().map(v => v.PastTense).filter(Boolean)
  const isPerfect = perfectVerbs.length > 0

  return {
    isPast,
    isProgressive,
    isPerfect,
    dominantTense: isPast ? 'past' : isProgressive ? 'progressive' : 'present'
  }
}

/**
 * 分析时间因素
 * @description 基于轨迹记录的时间戳计算时间相关的置信度加成
 * @param {Object} trackRecord - 轨迹记录对象
 * @param {string|number} trackRecord.msgTime - 消息时间戳
 * @param {string|number} trackRecord.time - 备用时间戳
 * @returns {Object} 时间分析结果
 * @returns {number} returns.timeBonus - 时间加成分数
 * @returns {boolean} returns.hasValidTime - 是否有有效时间
 * @returns {number} returns.hoursSinceUpdate - 距离更新的小时数
 * @returns {boolean} returns.isRecent - 是否为近期记录(24小时内)
 * @example
 * const timeAnalysis = analyzeTimeFactors(trackRecord)
 * console.log(timeAnalysis.timeBonus) // 5 (2小时内记录)
 * console.log(timeAnalysis.isRecent) // true
 */
function analyzeTimeFactors(trackRecord) {
  let timeBonus = 0
  const timestamp = trackRecord.msgTime || trackRecord.time

  if (!timestamp) {
    return { timeBonus: 0, hasValidTime: false }
  }

  try {
    const trackTime = new Date(timestamp)
    const now = new Date()
    const timeDiff = now - trackTime
    const hoursDiff = timeDiff / (1000 * 60 * 60)

    // 最新记录时间越近，置信度越高
    if (hoursDiff <= 2) {
      timeBonus += 5 // 2小时内的记录
    } else if (hoursDiff <= 24) {
      timeBonus += 3 // 24小时内的记录
    } else if (hoursDiff <= 72) {
      timeBonus += 1 // 3天内的记录
    }

    return {
      timeBonus,
      hasValidTime: true,
      hoursSinceUpdate: hoursDiff,
      isRecent: hoursDiff <= 24
    }
  } catch (error) {
    return { timeBonus: 0, hasValidTime: false }
  }
}

/**
 * 分析状态一致性
 * @description 分析状态序列中各状态的分布和一致性，确定主导状态
 * @param {Array<string>} statusSequence - 物流状态序列
 * @returns {Object} 一致性分析结果
 * @returns {boolean} returns.isConsistent - 是否一致(主导状态占比>=60%)
 * @returns {number} returns.confidence - 一致性置信度(0-1)
 * @returns {string} returns.dominantStatus - 主导状态
 * @returns {Object} returns.statusDistribution - 状态分布统计
 * @example
 * const consistency = analyzeStatusConsistency(['delivered', 'delivered', 'in_transit'])
 * console.log(consistency.isConsistent) // true
 * console.log(consistency.dominantStatus) // 'delivered'
 * console.log(consistency.confidence) // 0.67
 */
function analyzeStatusConsistency(statusSequence) {
  if (statusSequence.length <= 1) {
    return { isConsistent: true, confidence: 1, dominantStatus: statusSequence[0] }
  }

  // 统计各状态出现频率
  const statusCount = {}
  statusSequence.forEach(status => {
    statusCount[status] = (statusCount[status] || 0) + 1
  })

  const totalCount = statusSequence.length
  const maxCount = Math.max(...Object.values(statusCount))
  const dominantStatus = Object.keys(statusCount).find(status => statusCount[status] === maxCount)

  const consistency = maxCount / totalCount

  return {
    isConsistent: consistency >= 0.6,
    confidence: consistency,
    dominantStatus,
    statusDistribution: statusCount
  }
}

/**
 * 验证状态转换逻辑
 * @description 根据预定义的状态转换规则验证状态序列的合理性
 * @param {Array<string>} statusSequence - 状态序列（按时间倒序排列）
 * @returns {Object} 转换验证结果
 * @returns {boolean} returns.isValid - 转换是否有效
 * @returns {number} returns.confidence - 转换有效性置信度(0-1)
 * @returns {Array<Object>} returns.invalidTransitions - 无效转换列表
 * @example
 * const validation = validateStatusTransitions(['delivered', 'out_for_delivery', 'in_transit'])
 * console.log(validation.isValid) // true
 * console.log(validation.confidence) // 1.0
 * console.log(validation.invalidTransitions) // []
 */
function validateStatusTransitions(statusSequence) {
  if (statusSequence.length <= 1) {
    return { isValid: true, confidence: 1, invalidTransitions: [] }
  }

  // 定义合理的状态转换规则
  const validTransitions = {
    [LOGISTICS_STATUS.PENDING_PICKUP]: [LOGISTICS_STATUS.PICKED_UP, LOGISTICS_STATUS.EXCEPTION],
    [LOGISTICS_STATUS.PICKED_UP]: [LOGISTICS_STATUS.IN_TRANSIT, LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.EXCEPTION],
    [LOGISTICS_STATUS.IN_TRANSIT]: [LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.DELIVERED, LOGISTICS_STATUS.EXCEPTION],
    [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: [LOGISTICS_STATUS.DELIVERED, LOGISTICS_STATUS.FAILED_DELIVERY, LOGISTICS_STATUS.EXCEPTION],
    [LOGISTICS_STATUS.DELIVERED]: [], // 终态
    [LOGISTICS_STATUS.FAILED_DELIVERY]: [LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.RETURNED, LOGISTICS_STATUS.EXCEPTION],
    [LOGISTICS_STATUS.RETURNED]: [], // 终态
    [LOGISTICS_STATUS.EXCEPTION]: [LOGISTICS_STATUS.IN_TRANSIT, LOGISTICS_STATUS.OUT_FOR_DELIVERY, LOGISTICS_STATUS.RETURNED]
  }

  const invalidTransitions = []
  let validTransitionCount = 0

  // 检查相邻状态转换的合理性（注意：statusSequence是倒序的）
  for (let i = 0; i < statusSequence.length - 1; i++) {
    const currentStatus = statusSequence[i] // 较新的状态
    const previousStatus = statusSequence[i + 1] // 较旧的状态

    const allowedNextStates = validTransitions[previousStatus] || []

    if (allowedNextStates.includes(currentStatus) || currentStatus === previousStatus) {
      validTransitionCount++
    } else {
      invalidTransitions.push({
        from: previousStatus,
        to: currentStatus,
        index: i
      })
    }
  }

  const totalTransitions = statusSequence.length - 1
  const validityRatio = totalTransitions > 0 ? validTransitionCount / totalTransitions : 1

  return {
    isValid: validityRatio >= 0.7,
    confidence: validityRatio,
    invalidTransitions,
    validTransitionCount,
    totalTransitions
  }
}

/**
 * 分析上下文连贯性
 * @description 通过检测时间逻辑词、因果关系词和地点连续性词汇来评估文本连贯性
 * @param {string} context - 轨迹描述文本
 * @returns {number} 连贯性分数 (0-5)
 * @example
 * const coherence = analyzeContextCoherence('快件从北京发出，然后经过天津，最后抵达上海')
 * console.log(coherence) // 6 (时间逻辑+地点连续性)
 */
function analyzeContextCoherence(context) {
  let coherenceScore = 0

  // 检查时间逻辑词
  const timeLogicWords = ['然后', '接着', '随后', '之后', '现在', '目前']
  timeLogicWords.forEach(word => {
    if (context.includes(word)) coherenceScore += 2
  })

  // 检查因果关系词
  const causalWords = ['因为', '由于', '所以', '因此', '导致']
  causalWords.forEach(word => {
    if (context.includes(word)) coherenceScore += 3
  })

  // 检查地点连续性
  const locationWords = ['从', '到', '经过', '途径', '抵达']
  locationWords.forEach(word => {
    if (context.includes(word)) coherenceScore += 2
  })

  return Math.min(coherenceScore, 5) // 限制最大加分
}

/**
 * 确定最终状态
 * @description 综合轨迹分析、一致性分析和转换分析结果，确定最终的物流状态
 * @param {Array<Object>} trackAnalyses - 轨迹分析结果数组
 * @param {Object} consistencyAnalysis - 一致性分析结果
 * @param {Object} transitionAnalysis - 转换分析结果
 * @returns {Object} 最终状态结果
 * @returns {string} returns.status - 最终物流状态
 * @returns {string} returns.statusText - 状态文本描述
 * @returns {number} returns.confidence - 最终置信度
 * @example
 * const finalStatus = determineFinalStatus(trackAnalyses, consistencyAnalysis, transitionAnalysis)
 * console.log(finalStatus.status) // 'delivered'
 * console.log(finalStatus.confidence) // 0.85
 */
function determineFinalStatus(trackAnalyses, consistencyAnalysis, transitionAnalysis) {
  if (trackAnalyses.length === 0) {
    return {
      status: LOGISTICS_STATUS.PENDING_PICKUP,
      statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
      confidence: 0
    }
  }

  // 获取最新记录的分析结果
  const latestAnalysis = trackAnalyses[0]

  // 如果最新记录是终态且置信度高，直接返回
  if (isFinalStatus(latestAnalysis.status) && latestAnalysis.confidence >= 0.7) {
    return {
      status: latestAnalysis.status,
      statusText: STATUS_TEXT_MAP[latestAnalysis.status],
      confidence: Math.min(latestAnalysis.confidence + 0.1, 1)
    }
  }

  // 如果状态转换逻辑有效且一致性好，使用主导状态
  if (transitionAnalysis.isValid && consistencyAnalysis.isConsistent) {
    const finalStatus = consistencyAnalysis.dominantStatus
    const baseConfidence = Math.max(
      latestAnalysis.confidence,
      consistencyAnalysis.confidence * 0.8
    )

    return {
      status: finalStatus,
      statusText: STATUS_TEXT_MAP[finalStatus],
      confidence: Math.min(baseConfidence + 0.15, 1)
    }
  }

  // 如果转换逻辑无效，但最新记录置信度可接受，使用最新记录
  if (latestAnalysis.confidence >= 0.5) {
    return {
      status: latestAnalysis.status,
      statusText: STATUS_TEXT_MAP[latestAnalysis.status],
      confidence: latestAnalysis.confidence * 0.9 // 略微降低置信度
    }
  }

  // 最后的兜底策略：使用主导状态但降低置信度
  const fallbackStatus = consistencyAnalysis.dominantStatus || LOGISTICS_STATUS.PENDING_PICKUP
  return {
    status: fallbackStatus,
    statusText: STATUS_TEXT_MAP[fallbackStatus],
    confidence: Math.max(consistencyAnalysis.confidence * 0.6, 0.1)
  }
}

/**
 * 判断是否为终态状态
 * @description 判断给定的物流状态是否为终态（已签收、已退回、异常）
 * @param {string} status - 物流状态
 * @returns {boolean} 是否为终态
 * @example
 * console.log(isFinalStatus('delivered')) // true
 * console.log(isFinalStatus('in_transit')) // false
 */
export function isFinalStatus(status) {
  return [
    LOGISTICS_STATUS.DELIVERED,
    LOGISTICS_STATUS.RETURNED,
    LOGISTICS_STATUS.EXCEPTION
  ].includes(status)
}

/**
 * 获取状态的紧急程度
 * @description 根据物流状态返回对应的紧急程度等级
 * @param {string} status - 物流状态
 * @returns {number} 紧急程度 (1-5, 5最紧急)
 * @example
 * console.log(getStatusUrgency('exception')) // 5 (最紧急)
 * console.log(getStatusUrgency('delivered')) // 1 (不紧急)
 * console.log(getStatusUrgency('failed_delivery')) // 4 (较紧急)
 */
export function getStatusUrgency(status) {
  const urgencyMap = {
    [LOGISTICS_STATUS.EXCEPTION]: 5,
    [LOGISTICS_STATUS.FAILED_DELIVERY]: 4,
    [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: 3,
    [LOGISTICS_STATUS.IN_TRANSIT]: 2,
    [LOGISTICS_STATUS.PICKED_UP]: 2,
    [LOGISTICS_STATUS.PENDING_PICKUP]: 3,
    [LOGISTICS_STATUS.DELIVERED]: 1,
    [LOGISTICS_STATUS.RETURNED]: 4
  }

  return urgencyMap[status] || 2
}

/**
 * 获取详细的物流状态分析报告
 * @description 提供包含基础分析、紧急程度、建议等信息的详细物流分析报告
 * @param {Array<Object>} orderTrack - 物流轨迹数组
 * @returns {Object} 详细分析报告
 * @returns {string} returns.status - 物流状态
 * @returns {string} returns.statusText - 状态文本
 * @returns {number} returns.confidence - 置信度
 * @returns {number} returns.urgency - 紧急程度(1-5)
 * @returns {boolean} returns.isFinal - 是否为终态
 * @returns {Array<string>} returns.recommendations - 建议列表
 * @returns {number} returns.trackCount - 轨迹记录数量
 * @returns {string} returns.analysisTimestamp - 分析时间戳
 * @example
 * const report = getDetailedLogisticsAnalysis(orderTrack)
 * console.log(report.urgency) // 3
 * console.log(report.recommendations) // ['建议联系快递公司确认具体状态']
 */
export function getDetailedLogisticsAnalysis(orderTrack) {
  const basicAnalysis = analyzeLogisticsStatus(orderTrack)

  return {
    ...basicAnalysis,
    urgency: getStatusUrgency(basicAnalysis.status),
    isFinal: isFinalStatus(basicAnalysis.status),
    recommendations: generateRecommendations(basicAnalysis),
    trackCount: orderTrack?.length || 0,
    analysisTimestamp: new Date().toISOString()
  }
}

/**
 * 生成基于分析结果的建议
 * @description 根据物流状态分析结果生成相应的操作建议
 * @param {Object} analysis - 分析结果对象
 * @param {string} analysis.status - 物流状态
 * @param {number} analysis.confidence - 置信度
 * @param {Object} analysis.timeAnalysis - 时间分析结果
 * @returns {Array<string>} 建议列表
 * @example
 * const recommendations = generateRecommendations({
 *   status: 'failed_delivery',
 *   confidence: 0.3,
 *   timeAnalysis: { hoursSinceUpdate: 30 }
 * })
 * console.log(recommendations) // ['建议联系快递公司确认具体状态', '建议主动联系收件人确认地址和联系方式']
 */
function generateRecommendations(analysis) {
  const recommendations = []

  if (analysis.confidence < 0.5) {
    recommendations.push('建议联系快递公司确认具体状态')
  }

  if (analysis.status === LOGISTICS_STATUS.FAILED_DELIVERY) {
    recommendations.push('建议主动联系收件人确认地址和联系方式')
  }

  if (analysis.status === LOGISTICS_STATUS.EXCEPTION) {
    recommendations.push('建议立即联系快递公司处理异常情况')
  }

  if (analysis.status === LOGISTICS_STATUS.OUT_FOR_DELIVERY && analysis.timeAnalysis?.hoursSinceUpdate > 24) {
    recommendations.push('派送时间较长，建议联系派送员确认情况')
  }

  return recommendations
}
