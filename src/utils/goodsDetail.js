/**
 * @fileoverview 商品详情管理工具类
 * 提供商品规格选择、SKU管理、库存检查等功能
 * <AUTHOR>
 * @version 1.0.0
 */

import { pullAll, cloneDeep, intersection } from 'lodash-es'
import { log } from 'commonkit'
import { getGoodsDetail, isWhiteUserLimitCheck } from '@api/interface/goods.js'
import { getBizCode } from '@/utils/curEnv'
import { useUserStore } from '@/store/modules/user.js'

/**
 * 根据给定规格获取对应的规格组数据
 * @param {Array<Array<string>>} specsList - 规格列表数组
 * @param {string} spec - 目标规格值
 * @returns {Array<string>} 包含该规格的规格组
 * @example
 * const specsList = [['_p0_红色', '_p0_蓝色'], ['_p1_大', '_p1_小']]
 * getSpecsGroupFromSpec(specsList, '_p0_红色') // ['_p0_红色', '_p0_蓝色']
 */
const getSpecsGroupFromSpec = (specsList, spec) => {
  return specsList.filter(group => group.indexOf(spec) >= 0)[0]
}

/**
 * 根据规格数组获取匹配的SKU列表
 * @param {Array<Object>} skuList - SKU列表
 * @param {Array<string>} specs - 规格数组
 * @returns {Array<Object>} 匹配的SKU列表
 * @example
 * const skuList = [{param: '红色', param1: '大', skuId: '123'}]
 * getSkuListFromSpec(skuList, ['_p0_红色', '_p1_大']) // [{param: '红色', param1: '大', skuId: '123'}]
 */
const getSkuListFromSpec = (skuList, specs) => {
  let list = skuList

  specs.forEach(spec => {
    list = list.filter(sku => {
      return ('_p0_' + sku.param === spec) ||
             ('_p1_' + sku.param1 === spec) ||
             ('_p2_' + sku.param2 === spec) ||
             ('_p3_' + sku.param3 === spec)
    })
  })

  return list
}

/**
 * 根据参数名称获取对应的前缀
 * @param {string} name - 参数名称
 * @returns {string} 对应的前缀字符串
 * @example
 * paramPrefix('param') // '_p0_'
 * paramPrefix('param1') // '_p1_'
 */
const paramPrefix = (name) => {
  switch (name) {
    case 'param':
      return '_p0_'
    case 'param1':
      return '_p1_'
    case 'param2':
      return '_p2_'
    case 'param3':
      return '_p3_'
    default:
      return ''
  }
}

/**
 * 商品详情管理类
 * 提供商品规格选择、SKU管理、库存检查等核心功能
 * @class GoodsDetail
 */
class GoodsDetail {
  /** @type {string} 商品ID */
  #goodsId = ''

  /** @type {Object|null} 商品SPU详情数据 */
  #spu = null

  /** @type {Array<string>} 当前选中的规格列表，带有_p0_等前缀 */
  #curSpecs = []

  /** @type {string} 当前选中的SKU ID */
  #skuId = ''

  /** @type {Array<Array<string>>|null} 所有规格缓存，带有_p0_等前缀 */
  #specsList = null

  /** @type {Object} Pinia用户状态管理实例 */
  #userStore = null

  /**
   * 创建商品详情管理实例
   * @param {string} goodsId - 商品ID
   * @param {string} [skuId=''] - SKU ID，可选
   * @example
   * const goodsDetail = new GoodsDetail('12345', 'sku001')
   */
  constructor (goodsId, skuId) {
    this.#goodsId = goodsId
    this.#skuId = skuId || ''
    this.#userStore = useUserStore()
  }

  /**
   * 查询商品SPU信息并初始化默认SKU
   * @returns {Promise<Object>} 商品详情数据的深拷贝
   * @throws {Error} 当商品查询失败时抛出错误
   * @example
   * const result = await goodsDetail.querySpu()
   * console.log(result) // 商品详情数据
   */
  async querySpu () {
    // 先获取用户默认地址信息
    await this.#userStore.queryDefaultAddr()

    const info = this.#userStore.curAddressInfo
    const addressInfo = JSON.stringify({
      provinceId: info.provinceId,
      provinceName: info.provinceName,
      cityId: info.cityId,
      cityName: info.cityName,
      countyId: info.countyId,
      countyName: info.countyName,
      townId: info.townId,
      townName: info.townName
    })

    const [json, goodsDetail] = await getGoodsDetail({
      bizCode: getBizCode('GOODS'),
      goodsId: this.#goodsId,
      addressInfo: addressInfo
    })

    if (!goodsDetail) {
      return cloneDeep(json)
    }

    this.#spu = goodsDetail

    // 初始化默认SKU
    let flag = false
    let skuCache = {}

    if (this.#skuId) {
      // 使用指定的SKU ID
      const sku = this.#spu.skuList.filter(sku => this.#skuId === sku.skuId)[0]
      if (sku) skuCache = sku
    }

    // 如果没有有效的SKU缓存，则使用默认逻辑
    if (!skuCache.skuId) {
      // 查找第一个可用的SKU
      this.#spu.skuList.some(sku => {
        const { status } = this.checkSkuAvailable(sku)
        if (status === 0) {
          this.#skuId = sku.skuId
          skuCache = sku
          flag = true
          return true
        }
        return false
      })

      if (!flag) {
        // 所有SKU都不可用时，使用第一个SKU作为默认值
        this.#skuId = this.#spu.skuList[0].skuId
        skuCache = this.#spu.skuList[0]
      }
    }

    // 设置当前选中的规格
    const { param, param1, param2, param3 } = skuCache
    this.#curSpecs = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3].filter(p => !!p)

    return cloneDeep(json)
  }

  /**
   * 获取当前选中的SKU信息
   * @returns {Object} 当前SKU的深拷贝对象
   * @example
   * const sku = goodsDetail.querySku()
   * console.log(sku.skuId, sku.price) // SKU ID和价格
   */
  querySku () {
    const sku = this.#spu.skuList.filter(sku => sku.skuId === this.#skuId)[0]
    return cloneDeep(sku)
  }

  /**
   * 获取当前商品的SKU数量
   * 用于判断是否需要显示规格选择器
   * @returns {number} SKU数量
   * @example
   * const count = goodsDetail.querySkuCount()
   * if (count > 1) {
   *   // 显示规格选择器
   * } else {
   *   // 直接购买
   * }
   */
  querySkuCount () {
    return this.#spu.skuList.length
  }

  /**
   * 获取商品的所有规格列表
   * 返回四个规格组的二维数组，每个规格值都带有前缀
   * @returns {Array<Array<string>>} 规格列表，格式为[param组, param1组, param2组, param3组]
   * @example
   * const specsList = goodsDetail.querySpecsList()
   * // [['_p0_红色', '_p0_蓝色'], ['_p1_大', '_p1_小'], [], []]
   */
  querySpecsList () {
    if (this.#specsList) return this.#specsList

    const specs = [[], [], [], []]

    this.#spu.skuList.forEach(sku => {
      const { param, param1, param2, param3 } = sku

      if (param) {
        const has0 = specs[0].indexOf('_p0_' + param) >= 0
        if (!has0) specs[0].push('_p0_' + param)
      }

      if (param1) {
        const has1 = specs[1].indexOf('_p1_' + param1) >= 0
        if (!has1) specs[1].push('_p1_' + param1)
      }

      if (param2) {
        const has2 = specs[2].indexOf('_p2_' + param2) >= 0
        if (!has2) specs[2].push('_p2_' + param2)
      }

      if (param3) {
        const has3 = specs[3].indexOf('_p3_' + param3) >= 0
        if (!has3) specs[3].push('_p3_' + param3)
      }
    })

    this.#specsList = cloneDeep(specs)
    return this.#specsList
  }

  /**
   * 获取当前选中的规格列表（按标准顺序排序）
   * @returns {Array<string>} 当前选中的规格数组，按照商品标准规格顺序排序
   * @example
   * const curSpecs = goodsDetail.queryCurSpecs()
   * console.log(curSpecs) // ['_p0_红色', '_p1_大']
   */
  queryCurSpecs () {
    const specsList = this.querySpecsList()
    const newList = []

    // 按照标准规格顺序重新排序当前选中的规格
    specsList.forEach(item => {
      this.#curSpecs.forEach(curSpec => {
        if (item.indexOf(curSpec) >= 0) newList.push(curSpec)
      })
    })

    return newList
  }

  /**
   * 设置商品规格并更新对应的SKU
   * @param {string} spec - 要设置的规格值（带前缀）
   * @returns {boolean} 是否成功修改规格并找到唯一SKU
   * @example
   * const success = goodsDetail.setSpecs('_p0_红色')
   * if (success) {
   *   console.log('规格设置成功，SKU已更新')
   * }
   */
  setSpecs (spec) {
    // 检查规格是否可选
    if (this.queryDisabledSpecs().indexOf(spec) >= 0) return false

    let curSpecs = this.queryCurSpecs()
    const isSelected = curSpecs.filter(s => s === spec)[0]

    if (isSelected) {
      // 取消选中该规格
      curSpecs = curSpecs.filter(s => s !== spec)
    } else {
      // 选中该规格，需要先移除同组的其他规格
      const curGroup = getSpecsGroupFromSpec(this.querySpecsList(), spec)
      pullAll(curSpecs, curGroup)
      curSpecs.push(spec)
    }

    this.#curSpecs = curSpecs

    // 根据当前规格查找对应的SKU
    const list = getSkuListFromSpec(this.#spu.skuList, curSpecs)

    if (list.length === 1) {
      // 找到唯一SKU，更新当前SKU ID
      this.#skuId = list[0].skuId
      return true
    } else {
      // 多个或无SKU匹配，不更新SKU ID
      return false
    }
  }

  /**
   * 获取当前不可选择的规格列表
   * 基于当前已选规格和SKU可用性，计算出哪些规格不能被选择
   * @returns {Array<string>} 不可选择的规格数组
   * @example
   * const disabledSpecs = goodsDetail.queryDisabledSpecs()
   * console.log(disabledSpecs) // ['_p0_蓝色', '_p1_特大']
   */
  queryDisabledSpecs () {
    const specsList = this.querySpecsList()
    const curSpecs = this.queryCurSpecs()
    let disabledSpecs = [...specsList[0], ...specsList[1], ...specsList[2], ...specsList[3]]

    /**
     * 检查指定规格组的可用性
     * @param {Array<string>} specsListGroup - 规格组
     * @param {string} paramName - 参数名称
     */
    const runner = (specsListGroup, paramName) => {
      console.group(paramName)
      
      if (specsListGroup.length > 0) {
        // 排除当前规格组，获取其他已选规格
        const filteredSpecs = pullAll(cloneDeep(curSpecs), specsListGroup)
        log('[goodsDetail] queryDisabledSpecs', '排除：', specsListGroup, '后，当前剩余规格：', filteredSpecs)

        // 筛选可用的SKU列表
        const skuList = this.#spu.skuList.filter(sku => {
          const ret = this.checkSkuAvailable(sku)
          return ret.status === 0
        }).filter(sku => {
          // 如果没有其他已选规格，则所有SKU都有效
          if (filteredSpecs.length === 0) return true
          
          // 获取当前SKU的所有规格
          const { param, param1, param2, param3 } = sku
          const specs = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3].filter(p => !!p)
          
          // 计算规格交集，检查是否匹配已选规格
          const intersectionedSpecs = intersection(specs, filteredSpecs)
          return intersectionedSpecs.length === filteredSpecs.length
        })
        
        log('[goodsDetail] queryDisabledSpecs', '当前所有有效的skuList', skuList)

        // 提取当前规格组中的有效规格
        const validSpecs = skuList.map(sku => paramPrefix(paramName) + sku[paramName])
        log('[goodsDetail] queryDisabledSpecs', '当前有效的规格', validSpecs)

        // 从禁用列表中移除有效规格
        disabledSpecs = disabledSpecs.filter(spec => {
          return validSpecs.indexOf(spec) === -1
        })
        
        log('[goodsDetail] queryDisabledSpecs', '当前流程 disabledSpecs', disabledSpecs)
      }
      
      console.groupEnd(paramName)
    }

    // 依次检查四个规格组
    runner(specsList[0], 'param')
    runner(specsList[1], 'param1')
    runner(specsList[2], 'param2')
    runner(specsList[3], 'param3')

    log('[goodsDetail] queryDisabledSpecs', '当前不可选规格', disabledSpecs)
    return disabledSpecs
  }

  /**
   * 检查指定SKU是否可用
   * @param {Object} [sku] - 要检查的SKU对象，不传则检查当前SKU
   * @returns {{status: number, err?: string}} 检查结果
   * @returns {number} returns.status - 状态码：0-可用，1-无库存，2-已下架
   * @returns {string} [returns.err] - 错误信息
   * @example
   * const result = goodsDetail.checkSkuAvailable()
   * if (result.status === 0) {
   *   console.log('SKU可用')
   * } else {
   *   console.log('SKU不可用：', result.err)
   * }
   */
  checkSkuAvailable (sku) {
    const skuu = sku || this.querySku()
    
    // 检查商品状态：1-上架，其他-不可购买
    if (skuu.state !== '1') {
      return { status: 2, err: '该商品已下架，请选购其他商品~' }
    }
    
    // 检查库存
    if (Number(skuu.stock) <= 0) {
      return { status: 1, err: '所选地区暂时无货，非常抱歉！' }
    }
    
    return { status: 0 }
  }

  /**
   * 检查当前商品是否在限售区域内可用
   * @returns {Promise<boolean>} 是否可用
   * @example
   * const isAvailable = await goodsDetail.checkSpuAvailable()
   * if (isAvailable) {
   *   console.log('商品在当前区域可购买')
   * }
   */
  async checkSpuAvailable () {
    // 检查是否需要白名单验证
    if (this.#spu.isCheckWhiteUser === '1') {
      if (this.#userStore.isLogin) {
        const [err, json] = await isWhiteUserLimitCheck(this.#goodsId)
        if (!err && !json) return true
      }
    }
    return false
  }

  /**
   * 检查当前规格是否已全部选择完整
   * @returns {boolean} 规格是否选择完整
   * @example
   * const isComplete = goodsDetail.isSpecsComplete()
   * if (isComplete) {
   *   console.log('可以加入购物车')
   * } else {
   *   console.log('请选择完整规格')
   * }
   */
  isSpecsComplete () {
    const specsList = this.querySpecsList()
    const curSpecs = this.queryCurSpecs()
    
    // 计算有效的规格组数量
    const count = specsList.reduce((a, b) => {
      if (b.length > 0) a++
      return a
    }, 0)
    
    return count === curSpecs.length
  }
}

export default GoodsDetail

export const removeSpecPrefix = (param) => {
  if (typeof param === 'string') {
    // 特殊处理默认规格
    if (param === '默认规格') {
      return param
    }

    if (param.indexOf(',') >= 0) {
      return param.split(',').map(item => {
        // 检查是否有前缀
        if (item.startsWith('_p0_') || item.startsWith('_p1_') || item.startsWith('_p2_') || item.startsWith('_p3_')) {
          return item.substring(4)
        }
        return item
      }).join(',')
    } else {
      // 检查是否有前缀
      if (param.startsWith('_p0_') || param.startsWith('_p1_') || param.startsWith('_p2_') || param.startsWith('_p3_')) {
        return param.substring(4)
      }
      return param
    }
  } else if (Array.isArray(param)) {
    return param.map(item => {
      if (typeof item === 'string') {
        // 特殊处理默认规格
        if (item === '默认规格') {
          return item
        }
        // 检查是否有前缀
        if (item.startsWith('_p0_') || item.startsWith('_p1_') || item.startsWith('_p2_') || item.startsWith('_p3_')) {
          return item.substring(4)
        }
      }
      return item
    })
  }
  return param
}
