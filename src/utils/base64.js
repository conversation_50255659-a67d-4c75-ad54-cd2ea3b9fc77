/**
 * @fileoverview Base64编码工具
 * @description 提供字符串到Base64编码的转换功能，支持UTF-8编码
 */

/**
 * Base64字符映射表
 * @type {string[]}
 * @description 包含Base64编码使用的64个字符
 */
const BASE64_MAPPING = [
  'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
  'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P',
  'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
  'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f',
  'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n',
  'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
  'w', 'x', 'y', 'z', '0', '1', '2', '3',
  '4', '5', '6', '7', '8', '9', '+', '/'
]

/**
 * 将ASCII码转换为二进制数组
 * @param {number} ascii - ASCII码值
 * @returns {number[]} 二进制数组
 * @private
 */
const _toBinary = function (ascii) {
  const binary = []
  while (ascii > 0) {
    const b = ascii % 2
    ascii = Math.floor(ascii / 2)
    binary.push(b)
  }

  binary.reverse()
  return binary
}

/**
 * 将二进制数组转换为十进制数
 * @param {number[]} binary - 二进制数组
 * @returns {number} 十进制数值
 * @private
 */
const _toDecimal = function (binary) {
  let dec = 0
  let p = 0
  for (let i = binary.length - 1; i >= 0; --i) {
    const b = binary[i]
    if (b === 1) {
      dec += Math.pow(2, p)
    }
    ++p
  }
  return dec
}

/**
 * 将Unicode字符转换为UTF-8二进制编码
 * @param {number} c - UTF-8字节数
 * @param {number[]} binaryArray - 字符的二进制表示
 * @returns {number[]} UTF-8编码的二进制数组
 * @private
 */
const _toUTF8Binary = function (c, binaryArray) {
  const mustLen = (8 - (c + 1)) + ((c - 1) * 6)
  const fatLen = binaryArray.length
  let diff = mustLen - fatLen
  
  // 补充前导零
  while (--diff >= 0) {
    binaryArray.unshift(0)
  }
  
  const binary = []
  let _c = c
  
  // 添加UTF-8标识位
  while (--_c >= 0) {
    binary.push(1)
  }
  binary.push(0)
  
  let i = 0
  const len = 8 - (c + 1)
  
  // 添加第一个字节的数据位
  for (; i < len; ++i) {
    binary.push(binaryArray[i])
  }

  // 添加后续字节
  for (let j = 0; j < c - 1; ++j) {
    binary.push(1)
    binary.push(0)
    let sum = 6
    while (--sum >= 0) {
      binary.push(binaryArray[i++])
    }
  }
  
  return binary
}

/**
 * 将字符串编码为Base64格式
 * @param {string} str - 需要编码的字符串
 * @returns {string} Base64编码后的字符串
 * @description 支持UTF-8编码的字符串转Base64编码
 * @example
 * base64Encode('Hello World') // 'SGVsbG8gV29ybGQ='
 * base64Encode('你好') // '5L2g5aW9'
 */
export default (str) => {
  const base64Index = []
  let binaryArray = []
  
  // 遍历字符串中的每个字符
  for (let i = 0, len = str.length; i < len; ++i) {
    const unicode = str.charCodeAt(i)
    const tmpBinary = _toBinary(unicode)
    
    // 根据Unicode范围选择相应的UTF-8编码方式
    if (unicode < 0x80) {
      // ASCII字符，单字节编码
      let _tmpdiff = 8 - tmpBinary.length
      while (--_tmpdiff >= 0) {
        tmpBinary.unshift(0)
      }
      binaryArray = binaryArray.concat(tmpBinary)
    } else if (unicode >= 0x80 && unicode <= 0x7FF) {
      // 2字节UTF-8编码
      binaryArray = binaryArray.concat(_toUTF8Binary(2, tmpBinary))
    } else if (unicode >= 0x800 && unicode <= 0xFFFF) {
      // 3字节UTF-8编码
      binaryArray = binaryArray.concat(_toUTF8Binary(3, tmpBinary))
    } else if (unicode >= 0x10000 && unicode <= 0x1FFFFF) {
      // 4字节UTF-8编码
      binaryArray = binaryArray.concat(_toUTF8Binary(4, tmpBinary))
    } else if (unicode >= 0x200000 && unicode <= 0x3FFFFFF) {
      // 5字节UTF-8编码
      binaryArray = binaryArray.concat(_toUTF8Binary(5, tmpBinary))
    } else if (unicode >= 4000000 && unicode <= 0x7FFFFFFF) {
      // 6字节UTF-8编码
      binaryArray = binaryArray.concat(_toUTF8Binary(6, tmpBinary))
    }
  }

  let extraZeroCount = 0
  
  // 将二进制数组按6位分组转换为Base64索引
  for (let i = 0, len = binaryArray.length; i < len; i += 6) {
    const diff = (i + 6) - len
    
    // 计算需要补充的零位数
    if (diff === 2) {
      extraZeroCount = 2
    } else if (diff === 4) {
      extraZeroCount = 4
    }

    // 补充零位
    let _tmpExtraZeroCount = extraZeroCount
    while (--_tmpExtraZeroCount >= 0) {
      binaryArray.push(0)
    }
    
    // 转换为Base64索引
    base64Index.push(_toDecimal(binaryArray.slice(i, i + 6)))
  }

  // 根据索引生成Base64字符串
  let base64 = ''
  for (let i = 0, len = base64Index.length; i < len; ++i) {
    base64 += BASE64_MAPPING[base64Index[i]]
  }

  // 添加填充字符
  for (let i = 0, len = extraZeroCount / 2; i < len; ++i) {
    base64 += '='
  }
  
  return base64
}
