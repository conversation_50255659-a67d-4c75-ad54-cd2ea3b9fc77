/**
 * @fileoverview 本地存储管理模块
 * @description 统一管理应用中所有的本地存储键值对，包括业务数据、用户信息、商品信息等
 * <AUTHOR>
 * @since 1.0.0
 */

import { storage } from 'commonkit'

// ==================== 业务相关存储 ====================

/**
 * 分销业务码存储
 * @description 存储当前分销业务的原始业务码
 * @type {Object}
 */
export const curDistriBiz = storage('PS_CCMS_DISTRI_BIZ')

/**
 * 渠道业务码存储
 * @description 存储当前渠道的业务码标识
 * @type {Object}
 */
export const curChannelBiz = storage('PS_CCMS_CHANNEL_BIZ')

/**
 * 发展人ID存储
 * @description 存储推荐人或发展人的唯一标识
 * @type {Object}
 */
export const curDeveloperId = storage('PS_CCMS_DEVELOPER_ID')

/**
 * 京东开普勒引导页时间控制
 * @description 控制京东开普勒引导页的显示时间，使用localStorage存储
 * @type {Object}
 */
export const curJdGuidePageDate = storage('PS_CCMS_JD_GUIDE_PAGE', true)

/**
 * 订单确认页分期编码存储
 * @description 存储用户在订单确认页选择的分期付款编码
 * @type {Object}
 */
export const curLoanCode = storage('PS_CCMS_LOAN_CODE')

/**
 * 当前商品池ID存储
 * @description 存储当前选中的商品分类池ID
 * @type {Object}
 */
export const curClassificationId = storage('PS_CCMS_CLASSIFICATION_ID')

/**
 * 扶贫商城荣誉证书缓存
 * @description 缓存扶贫商城荣誉证书图片的base64数据，使用localStorage存储
 * @type {Object}
 */
export const fpCertificateCache = storage('PS_CCMS_FP_CERTIFICATE', true)

/**
 * 首页浮标控制存储
 * @description 控制首页浮动标签的显示状态
 * @type {Object}
 */
export const indexFloatTag = storage('PS_CCMS_INDEX_FLOAT_TAG')

/**
 * 省分助农分类ID存储
 * @description 存储省分助农页面地址栏获取的分类ID
 * @type {Object}
 */
export const categoryPid = storage('PS_CCMS_CATEGORY_PID')

/**
 * 用户临时地址信息存储
 * @description 缓存用户临时填写的地址信息
 * @type {Object}
 */
export const curTempAddrInfo = storage('PS_CCMS_TEMP_ADDR_INFO')

/**
 * 登录类型存储
 * @description 存储当前渠道对应的登录类型（0-默认模式，1-查询政企数据模式）
 * @type {Object}
 */
export const loginType = storage('PS_CCMS_LOGIN_TYPE')

/**
 * 政企角色存储
 * @description 存储政企渠道当前登录角色，通过URL参数获取，仅在多角色选择条件下启用
 * @type {Object}
 */
export const zqRole = storage('PS_CCMS_ZQ_ROLE')
// ==================== 商品相关存储 ====================

/**
 * 立即购买商品信息存储（本地存储）
 * @description 存储用户立即购买的商品信息，使用localStorage持久化存储
 * @type {Object}
 */
export const buyProductNow = storage('PS_CCMS_BUY_NOW_PRODUCT', true)

/**
 * 购物车商品信息存储（本地存储）
 * @description 存储用户购物车中的商品信息，使用localStorage持久化存储
 * @type {Object}
 */
export const buyProductCart = storage('PS_CCMS_BUY_CART_PRODUCT', true)

/**
 * 立即购买商品信息存储（会话存储）
 * @description 存储用户立即购买的商品信息，使用sessionStorage会话级存储
 * @type {Object}
 */
export const buyProductNowSession = storage('PS_CCMS_BUY_NOW_PRODUCT', false)

/**
 * 购物车商品信息存储（会话存储）
 * @description 存储用户购物车中的商品信息，使用sessionStorage会话级存储
 * @type {Object}
 */
export const buyProductCartSession = storage('PS_CCMS_BUY_CART_PRODUCT', false)

/**
 * 售后商品信息存储
 * @description 存储需要进行售后处理的商品信息，使用sessionStorage存储
 * @type {Object}
 */
export const afterSalesProduct = storage('PS_CCMS_AFTER_SALES_PRODUCT', false)
