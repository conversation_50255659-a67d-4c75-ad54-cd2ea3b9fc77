/**
 * @fileoverview 购物车管理模块 - 基于Pinia的购物车状态管理
 * 
 * 提供完整的购物车功能，包括商品管理、状态同步、库存检查等
 * 支持有效商品和失效商品的分组管理，提供丰富的计算属性和操作方法
 * 
 * @module store/modules/newCart
 * @requires pinia
 * @requires @/api/cart
 * @requires @/utils/curEnv
 * @requires ./user
 * @requires commonkit
 * @requires dayjs
 * @requires @/utils/zqInfo
 * @requires lodash-es
 * 
 * @example
 * // 基本使用
 * const cartStore = useNewCartStore()
 * 
 * // 查询购物车
 * const err = await cartStore.query()
 * 
 * // 获取购物车数据
 * const validList = cartStore.validList // 有效商品列表
 * const invalidList = cartStore.invalidList // 失效商品列表
 * const totalCount = cartStore.countAll // 所有商品数量
 * const selectedCount = cartStore.selectCountAll // 选中商品数量
 * const totalPrice = cartStore.selectTotalPrice // 选中商品总价
 * 
 * // 商品操作
 * await cartStore.add({ goodsId: '123', skuId: '456', goodsNum: 2, addressInfo: {} })
 * await cartStore.updateGoodsNum({ goodsId: '123', skuId: '456', goodsNum: 3 })
 * await cartStore.removeMuti([{ goodsId: '123', skuId: '456' }])
 * 
 * // 批量选择操作
 * await cartStore.checkedAllValid() // 全选/反选
 * await cartStore.checkedAllByGroupName('groupName') // 分组全选/反选
 */
import { defineStore } from 'pinia'
import { cart } from '@/api'
import { getBizCode } from '@/utils/curEnv'
import { useUserStore } from './user'
import { woReport } from 'commonkit'
import dayjs from 'dayjs'
import { queryZqInfo } from '@/utils/zqInfo'
import { cloneDeep } from 'lodash-es'

/**
 * 数据保护装饰器 - 通过深拷贝隔离原始数据
 * 防止外部直接修改store内部数据，确保数据的不可变性
 * 
 * @function protectData
 * @param {any} data - 需要保护的数据
 * @returns {any} 深拷贝后的数据
 * 
 * @example
 * const originalData = { name: 'test', items: [1, 2, 3] }
 * const protectedData = protectData(originalData)
 * protectedData.name = 'modified' // 不会影响原始数据
 */
const protectData = (data) => {
  return cloneDeep(data)
}

/**
 * 数据完整性验证 - 验证商品分组数据的有效性
 * 确保分组数据结构正确，防止因数据异常导致的运行时错误
 * 
 * @function validateGroupData
 * @param {Object} group - 商品分组数据
 * @param {string} group.groupName - 分组名称
 * @param {Array} group.goodsList - 商品列表
 * @param {boolean} [group.selected] - 分组选中状态
 * @returns {boolean} 验证结果，true表示数据有效
 * 
 * @example
 * const validGroup = { groupName: '默认分组', goodsList: [], selected: false }
 * const isValid = validateGroupData(validGroup) // true
 * 
 * const invalidGroup = { groupName: '测试', goodsList: 'invalid' }
 * const isInvalid = validateGroupData(invalidGroup) // false
 */
const validateGroupData = (group) => {
  if (!group || typeof group !== 'object') {
    console.warn('无效的分组数据:', group)
    return false
  }
  if (!Array.isArray(group.goodsList)) {
    console.warn('分组商品列表格式错误:', group)
    return false
  }
  return true
}

/**
 * 创建商品索引Map - 提高商品查找性能
 * 将所有商品（有效和无效）建立索引，支持O(1)时间复杂度的快速查找
 * 
 * @function createGoodsMap
 * @param {Object} state - store状态对象
 * @param {Array} state.validList - 有效商品分组列表
 * @param {Array} state.invalidList - 无效商品分组列表
 * @returns {Map<string, Object>} 商品索引Map，key为"goodsId_skuId"格式
 * 
 * @example
 * const state = {
 *   validList: [{ goodsList: [{ cartGoodsId: '123', cartSkuId: '456' }] }],
 *   invalidList: []
 * }
 * const goodsMap = createGoodsMap(state)
 * const goods = goodsMap.get('123_456') // { goods: {...}, type: 'valid', group: {...} }
 */
const createGoodsMap = (state) => {
  const goodsMap = new Map()

  // 索引有效商品
  state.validList.forEach(group => {
    group.goodsList?.forEach(goods => {
      const key = `${goods.cartGoodsId}_${goods.cartSkuId}`
      goodsMap.set(key, { goods, type: 'valid', group })
    })
  })

  // 索引无效商品
  state.invalidList.forEach(group => {
    group.goodsList?.forEach(goods => {
      const key = `${goods.cartGoodsId}_${goods.cartSkuId}`
      goodsMap.set(key, { goods, type: 'invalid', group })
    })
  })

  return goodsMap
}

/**
 * 通过商品ID和SKU ID获取商品信息 - 高性能版本
 * 使用索引Map实现快速查找，避免嵌套循环遍历
 * 
 * @function getGoods
 * @param {Object} state - store状态对象
 * @param {string|number} goodsId - 商品ID
 * @param {string|number} skuId - SKU ID
 * @returns {Object|null} 商品信息对象，未找到时返回null
 * 
 * @example
 * const goods = getGoods(state, '123', '456')
 * if (goods) {
 *   console.log('商品名称:', goods.goodsName)
 *   console.log('商品数量:', goods.skuNum)
 * }
 */
const getGoods = (state, goodsId, skuId) => {
  const goodsMap = createGoodsMap(state)
  const key = `${goodsId}_${skuId}`
  const result = goodsMap.get(key)
  return result?.goods || null
}

/**
 * 获取指定选中状态的商品列表 - 支持分组过滤
 * 根据选中状态和可选的分组名称筛选商品，用于批量操作
 * 
 * @function getSelectGoods
 * @param {Object} state - store状态对象
 * @param {boolean} select - 目标选中状态，true获取已选中商品，false获取未选中商品
 * @param {string} [groupName=''] - 可选的分组名称，为空时查询所有分组
 * @returns {Array<Object>} 符合条件的商品列表
 * 
 * @example
 * // 获取所有已选中的商品
 * const selectedGoods = getSelectGoods(state, true)
 * 
 * // 获取指定分组中未选中的商品
 * const unselectedGoods = getSelectGoods(state, false, '默认分组')
 * 
 * // 用于批量操作
 * const goodsToRemove = getSelectGoods(state, true).map(goods => ({
 *   goodsId: goods.cartGoodsId,
 *   skuId: goods.cartSkuId
 * }))
 */
const getSelectGoods = (state, select, groupName = '') => {
  const selected = select ? 'true' : 'false'
  const result = []

  state.validList.forEach(group => {
    if (groupName && group.groupName !== groupName) return

    group.goodsList?.forEach(goods => {
      if (goods.selected === selected) {
        result.push(goods)
      }
    })
  })

  return result
}



/**
 * 购物车查询状态常量
 * 用于标识购物车数据加载的不同阶段
 * 
 * @constant {Object} CART_QUERY_STATUS
 * @property {string} SUCCESS - 查询成功状态
 * @property {string} FAILED - 查询失败状态
 * @property {string} LOADING - 查询加载中状态
 * 
 * @example
 * // 检查购物车加载状态
 * if (cartStore.cartLoadingStatus === CART_QUERY_STATUS.LOADING) {
 *   console.log('购物车数据加载中...')
 * }
 */
export const CART_QUERY_STATUS = {
  SUCCESS: 'success',
  FAILED: 'failed',
  LOADING: 'loading'
}

/**
 * 购物车状态管理Store
 * 基于Pinia实现的购物车状态管理，提供完整的购物车功能
 * 
 * @function useNewCartStore
 * @returns {Object} Pinia store实例
 * 
 * @example
 * // 在组件中使用
 * import { useNewCartStore } from '@/store/modules/newCart'
 * 
 * const cartStore = useNewCartStore()
 * await cartStore.query() // 查询购物车数据
 * console.log(cartStore.countAll) // 获取商品总数
 */
export const useNewCartStore = defineStore('newCart', {
  /**
   * Store状态定义
   * 
   * @returns {Object} 初始状态对象
   * @property {Array<Object>} validList - 有效商品分组列表
   * @property {Array<Object>} invalidList - 失效商品分组列表  
   * @property {string} cartLoadingStatus - 购物车加载状态
   */
  state: () => ({
    /** @type {Array<{groupName: string, goodsList: Array, selected: boolean}>} 有效商品分组列表 */
    validList: [],
    /** @type {Array<{groupName: string, goodsList: Array}>} 失效商品分组列表 */
    invalidList: [],
    /** @type {string} 购物车加载状态 */
    cartLoadingStatus: CART_QUERY_STATUS.LOADING
  }),
  /**
   * 计算属性 - 提供购物车数据的各种计算结果
   */
  getters: {
    /**
     * 获取所有有效商品的总数量
     * 计算所有分组中商品的数量总和
     * 
     * @returns {number} 商品总数量
     * 
     * @example
     * const totalCount = cartStore.countAll
     * console.log(`购物车共有 ${totalCount} 件商品`)
     */
    countAll() {
      return this.validList.reduce((count, group) => {
        return count + (group.goodsList?.reduce((sum, goods) => sum + (goods.skuNum || 0), 0) || 0)
      }, 0)
    },

    /**
     * 获取购物车加载状态
     * 
     * @returns {string} 加载状态值
     * 
     * @example
     * const status = cartStore.getCartLoadingStatus
     * if (status === CART_QUERY_STATUS.LOADING) {
     *   // 显示加载中状态
     * }
     */
    getCartLoadingStatus() {
      return this.cartLoadingStatus
    },

    /**
     * 获取有效商品列表
     * 
     * @returns {Array<Object>} 有效商品分组列表
     * 
     * @example
     * const validList = cartStore.getCartValidList
     * validList.forEach(group => {
     *   console.log(`分组: ${group.groupName}, 商品数: ${group.goodsList.length}`)
     * })
     */
    getCartValidList() {
      return this.validList
    },

    /**
     * 获取失效商品列表
     * 
     * @returns {Array<Object>} 失效商品分组列表
     * 
     * @example
     * const invalidList = cartStore.getCartInvalidList
     * if (invalidList.length > 0) {
     *   console.log('存在失效商品，需要处理')
     * }
     */
    getCartInvalidList() {
      return this.invalidList
    },
    /**
     * 获取所有有效商品的种类数
     * 计算所有分组中商品的种类总数，当登录用户商品数为0时自动重新查询
     * 
     * @returns {number} 商品种类总数
     * 
     * @example
     * const goodsTypes = cartStore.countByGoods
     * console.log(`购物车共有 ${goodsTypes} 种商品`)
     */
    countByGoods() {
      const count = this.validList.reduce((count, group) => {
        return count + (group.goodsList?.length || 0)
      }, 0)

      // 如果登录态且数量为0，重新拉取购物车数据
      if (count === 0) {
        const userStore = useUserStore()
        if (userStore.isLogin === true) {
          this.query()
        }
      }

      return count
    },

    /**
     * 获取选中商品的总数量
     * 计算所有已选中商品的数量总和
     * 
     * @returns {number} 选中商品总数量
     * 
     * @example
     * const selectedCount = cartStore.selectCountAll
     * console.log(`已选中 ${selectedCount} 件商品`)
     */
    selectCountAll() {
      return this.validList.reduce((count, group) => {
        return count + (group.goodsList?.reduce((sum, goods) => {
          return sum + (goods.selected === 'true' ? (goods.skuNum || 0) : 0)
        }, 0) || 0)
      }, 0)
    },

    /**
     * 获取选中商品的种类数
     * 计算已选中商品的种类总数
     * 
     * @returns {number} 选中商品种类数
     * 
     * @example
     * const selectedTypes = cartStore.selectCountByGoods
     * console.log(`已选中 ${selectedTypes} 种商品`)
     */
    selectCountByGoods() {
      return this.validList.reduce((count, group) => {
        return count + (group.goodsList?.filter(goods => goods.selected === 'true').length || 0)
      }, 0)
    },
    /**
     * 获取选中商品的总价格
     * 计算所有已选中商品的价格总和
     * 
     * @returns {number} 选中商品总价格
     * 
     * @example
     * const totalPrice = cartStore.selectTotalPrice
     * console.log(`总价: ¥${totalPrice.toFixed(2)}`)
     */
    selectTotalPrice() {
      return this.validList.reduce((total, group) => {
        return total + (group.goodsList?.reduce((sum, goods) => {
          if (goods.selected === 'true') {
            const price = Number(goods.nowPrice) || 0
            const num = Number(goods.skuNum) || 0
            return sum + (price * num)
          }
          return sum
        }, 0) || 0)
      }, 0)
    },

    /**
     * 判断是否全选
     * 检查所有有效商品是否都已选中
     * 
     * @returns {boolean} 是否全选
     * 
     * @example
     * const isAllSelected = cartStore.isSelectAll
     * if (isAllSelected) {
     *   console.log('所有商品已选中')
     * }
     */
    isSelectAll() {
      if (this.validList.length === 0) return false
      return this.validList.every(group => group.selected === true)
    },

    /**
     * 判断是否有商品
     * 检查购物车中是否存在有效商品
     * 
     * @returns {boolean} 是否有商品
     * 
     * @example
     * if (cartStore.hasGoods) {
     *   // 显示购物车内容
     * } else {
     *   // 显示空购物车提示
     * }
     */
    hasGoods() {
      return this.countByGoods > 0
    },

    /**
     * 判断是否有选中的商品
     * 检查是否存在已选中的商品
     * 
     * @returns {boolean} 是否有选中商品
     * 
     * @example
     * if (cartStore.hasSelectedGoods) {
     *   // 显示结算按钮
     * } else {
     *   // 禁用结算按钮
     * }
     */
    hasSelectedGoods() {
      return this.selectCountByGoods > 0
    },

    /**
     * 判断是否有失效商品
     * 检查失效商品列表中是否存在商品
     * 
     * @returns {boolean} 是否有失效商品
     * 
     * @example
     * if (cartStore.hasInvalidGoods) {
     *   // 显示失效商品提示
     * }
     */
    hasInvalidGoods() {
      return this.invalidList.some(group => group.goodsList?.length > 0)
    },

    /**
     * 判断是否有有效商品
     * 检查有效商品列表中是否存在商品
     * 
     * @returns {boolean} 是否有有效商品
     * 
     * @example
     * if (cartStore.hasValidGoods) {
     *   // 显示购物车操作按钮
     * }
     */
    hasValidGoods() {
      return this.validList.some(group => group.goodsList?.length > 0)
    }
  },
  /**
   * 操作方法 - 提供购物车的各种操作功能
   */
  actions: {
    /**
     * 设置有效商品列表
     * 更新购物车中的有效商品数据，自动进行数据保护处理
     * 
     * @param {Array<Object>} payload - 有效商品分组列表
     * @param {string} payload[].groupName - 分组名称
     * @param {Array<Object>} payload[].goodsList - 商品列表
     * @param {boolean} payload[].selected - 分组选中状态
     * 
     * @example
     * cartStore.setValidList([
     *   {
     *     groupName: '自营商品',
     *     goodsList: [{ skuId: '123', skuNum: 2, selected: 'true' }],
     *     selected: true
     *   }
     * ])
     */
    setValidList(payload) {
      // 数据验证 + 深拷贝保护
      const validatedPayload = payload.filter(validateGroupData)
      this.validList = protectData(validatedPayload)
      this.selectCheck()
    },

    /**
     * 设置失效商品列表
     * 更新购物车中的失效商品数据，自动进行数据保护处理
     * 
     * @param {Array<Object>} payload - 失效商品分组列表
     * @param {string} payload[].groupName - 分组名称
     * @param {Array<Object>} payload[].goodsList - 失效商品列表
     * 
     * @example
     * cartStore.setInvalidList([
     *   {
     *     groupName: '失效商品',
     *     goodsList: [{ skuId: '456', reason: '商品下架' }]
     *   }
     * ])
     */
    setInvalidList(payload) {
      // 数据验证 + 深拷贝保护
      const validatedPayload = payload.filter(validateGroupData)
      this.invalidList = protectData(validatedPayload)
    },

    /**
     * 设置购物车加载状态
     * 更新购物车的加载状态标识
     * 
     * @param {string} payload - 加载状态值
     * 
     * @example
     * cartStore.setCartLoadingStatus(CART_QUERY_STATUS.LOADING)
     * cartStore.setCartLoadingStatus(CART_QUERY_STATUS.SUCCESS)
     */
    setCartLoadingStatus(payload) {
      this.cartLoadingStatus = payload
    },
    /**
     * 批量更新商品信息
     * 根据提供的商品列表批量更新购物车中的商品信息
     * 
     * @param {Array<Object>} payload - 要更新的商品列表
     * @param {string|number} payload[].goodsId - 商品ID
     * @param {string|number} payload[].skuId - 商品SKU ID
     * @param {number} [payload[].goodsNum] - 商品数量
     * @param {boolean} [payload[].select] - 选中状态
     * 
     * @example
     * cartStore.updateGoodsMuti([
     *   { goodsId: '123', skuId: '456', goodsNum: 3, select: true },
     *   { goodsId: '789', skuId: '012', goodsNum: 1, select: false }
     * ])
     */
    updateGoodsMuti(payload) {
      const newState = cloneDeep(this.$state)
      payload.forEach(load => {
        const { goodsId, skuId, goodsNum, select } = load
        const goods = getGoods(newState, goodsId, skuId)
        if (!goods) return
        if (typeof goodsNum === 'number') goods.skuNum = goodsNum
        if (typeof select === 'boolean') goods.selected = select ? 'true' : 'false'
      })
      this.setValidList(newState.validList)
      this.selectCheck()
    },

    /**
     * 批量移除商品
     * 根据商品ID和SKU ID列表批量移除购物车中的商品
     * 
     * @param {Array<Object>} payload - 要移除的商品列表
     * @param {string|number} payload[].goodsId - 商品ID
     * @param {string|number} payload[].skuId - 商品SKU ID
     * 
     * @example
     * cartStore.removeGoodsMuti([
     *   { goodsId: '123', skuId: '456' },
     *   { goodsId: '789', skuId: '012' }
     * ])
     */
    removeGoodsMuti(payload) {
      const newState = cloneDeep(this.$state)
      payload.forEach(load => {
        const { goodsId, skuId } = load
        newState.validList.forEach(group => {
          group.goodsList = group.goodsList.filter(goods => {
            return !(goods.cartGoodsId === goodsId && goods.cartSkuId === skuId)
          })
        })
        newState.invalidList.forEach(group => {
          group.goodsList = group.goodsList.filter(goods => {
            return !(goods.cartGoodsId === goodsId && goods.cartSkuId === skuId)
          })
        })
      })
      this.setValidList(newState.validList)
      this.setInvalidList(newState.invalidList)
      this.hasGoodsCheck()
      this.selectCheck()
    },
    /**
     * 选中状态检查和更新
     * 根据商品的选中状态更新分组的选中状态
     * 当分组内所有商品都选中时，分组状态为选中；否则为未选中
     * 
     * @example
     * // 在更新商品选中状态后调用
     * cartStore.selectCheck()
     */
    selectCheck() {
      const newState = cloneDeep(this.$state)
      newState.validList.forEach(group => {
        if (group.goodsList && group.goodsList.length > 0) {
          const selectedList = group.goodsList.filter(goods => goods.selected === 'true')
          group.selected = selectedList.length === group.goodsList.length
        } else {
          group.selected = false
        }
      })

      newState.validList.forEach(group => {
        protectData(group)
      })

      this.validList = newState.validList
    },

    /**
     * 检查商品分组是否还有商品
     * 过滤掉空的商品分组，保持购物车数据的整洁性
     * 本方法只是过滤空 group 数据，不改变存在数据的 group
     * 
     * @example
     * // 在删除商品后调用，清理空分组
     * cartStore.hasGoodsCheck()
     */
    hasGoodsCheck() {
      const newValidList = this.validList.filter(group => {
        return group.goodsList && group.goodsList.length > 0
      })
      this.validList = newValidList
    },
    /**
     * 登录后处理
     * 用户登录后自动查询购物车数据
     * 
     * @example
     * // 用户登录成功后调用
     * cartStore.login()
     */
    async login() {
      const userStore = useUserStore()
      await userStore.login({ reload: false })
    },
    /**
     * 查询购物车数据
     * 根据地址信息查询购物车中的商品列表，包括有效商品和失效商品
     * 
     * @param {Object} [addressInfo] - 地址信息，不传则使用用户默认地址
     * @param {string} addressInfo.provinceId - 省份ID
     * @param {string} addressInfo.cityId - 城市ID
     * @param {string} addressInfo.countyId - 区县ID
     * @param {string} addressInfo.townId - 乡镇ID
     * 
     * @returns {Promise<void>}
     * 
     * @example
     * // 使用默认地址查询
     * await cartStore.query()
     * 
     * // 使用指定地址查询
     * await cartStore.query({
     *   provinceId: '110000',
     *   cityId: '110100',
     *   countyId: '110101',
     *   townId: '110101001'
     * })
     */
    async query() {
      // 需要先读取地址，再查询购物车
      const userStore = useUserStore()
      await userStore.queryDefaultAddr()
      const addr = userStore.curAddressInfo
      const addressInfo = JSON.stringify({
        provinceId: addr.provinceId,
        provinceName: addr.provinceName,
        cityId: addr.cityId,
        cityName: addr.cityName,
        countyId: addr.countyId,
        countyName: addr.countyName,
        townId: addr.townId,
        townName: addr.townName
      })
      this.setCartLoadingStatus(CART_QUERY_STATUS.LOADING)

      const bizCode = getBizCode('ORDER')
      const zqInfo = queryZqInfo()
      let params = {
        bizCode,
        addressInfo
      }

      if (bizCode === 'zq') {
        params = {
          bizCode,
          addressInfo,
          enterpriseCode: zqInfo.ciCode
        }
      }

      const [err, json] = await cart.query(params)
      if (!err) {
        this.setCartLoadingStatus(CART_QUERY_STATUS.SUCCESS)
        if (json && !json.goodsGroupList) {
          const timestamp = dayjs().format('YYYY-MM-DD hh:mm:ss')
          const params = {
            cartId: json.cartId,
            distriBizCode: json.distriBizCode
          }
          woReport(`购物车监控，分组数据出现丢失。 出现问题时间：${timestamp}`, params)
        }

        // 现要求所有分组合并成一组，特殊处理
        const list1 = json.goodsGroupList.map(group => {
          group.selected = false
          return group
        })

        const list2 = json.invalidGoodsList.filter(goods => {
          return goods.goods
        })
        const validList = list1.length > 0 ? list1 : []
        const invalidList = list2.length > 0 ? [{ groupName: '失效商品', goodsList: list2 }] : []

        this.setValidList(validList)
        this.setInvalidList(invalidList)
        return null
      } else {
        this.setCartLoadingStatus(CART_QUERY_STATUS.FAILED)
        return err
      }
    },
    /**
     * 快速查询购物车
     * 不需要地址信息的快速购物车查询，适用于简单的购物车数据获取
     * 
     * @returns {Promise<void>}
     * 
     * @example
     * // 快速获取购物车数据，不考虑地址相关的库存和价格
     * await cartStore.queryQuick()
     */
    async queryQuick() {
      // 需要先读取地址，再查询购物车
      const userStore = useUserStore()
      await userStore.queryDefaultAddr()
      const addr = userStore.curAddressInfo
      const addressInfo = JSON.stringify({
        provinceId: addr.provinceId,
        provinceName: addr.provinceName,
        cityId: addr.cityId,
        cityName: addr.cityName,
        countyId: addr.countyId,
        countyName: addr.countyName,
        townId: addr.townId,
        townName: addr.townName
      })
      this.setCartLoadingStatus(CART_QUERY_STATUS.LOADING)
      const [err, json] = await cart.baseView({ bizCode: getBizCode('ORDER'), addressInfo })
      if (!err) {
        this.setCartLoadingStatus(CART_QUERY_STATUS.SUCCESS)
        if (json && !json.goodsGroupList) {
          const timestamp = dayjs().format('YYYY-MM-DD hh:mm:ss')
          const params = {
            cartId: json.cartId,
            distriBizCode: json.distriBizCode
          }
          woReport(`购物车监控，分组数据出现丢失。 出现问题时间：${timestamp}`, params)
        }

        const oldCartValidList = this.validList
        let newCartValidList = json.goodsGroupList
        const oldCartInvalidList = this.invalidList
        // 1. 先看看分组是否勾选选了
        const oldItemsMap = new Map(oldCartValidList.map(item => [item.groupName, item.selected]))
        newCartValidList.forEach(newItem => {
          const selected = oldItemsMap.get(newItem.groupName)
          if (selected !== undefined) {
            newItem.selected = selected
          }
        })

        console.warn('oldCartInvalidList', oldCartInvalidList)

        // 创建一个包含所有无效商品ID的集合
        const invalidIds = new Set()
        oldCartInvalidList.forEach(oldItem => {
          oldItem.goodsList.forEach(oldCartInvalidItem => {
            invalidIds.add(oldCartInvalidItem.cartSkuId)
          })
        })

        // 使用这个集合来过滤newCartValidList中的有效商品
        newCartValidList = newCartValidList.map(newItem => {
          return {
            ...newItem,
            goodsList: newItem.goodsList.filter(item => !invalidIds.has(item.cartSkuId))
          }
        })

        console.warn('newCartValidList', newCartValidList)

        const validList = newCartValidList.length > 0 ? newCartValidList : []
        this.setValidList(validList)
        return null
      } else {
        this.setCartLoadingStatus(CART_QUERY_STATUS.FAILED)
        return err
      }
    },
    /**
     * 添加商品到购物车
     * 将指定商品添加到购物车中，支持指定数量和地址信息
     * 
     * @param {Object} payload - 添加商品的参数对象
     * @param {string|number} payload.goodsId - 商品ID
     * @param {string|number} payload.skuId - 商品SKU ID
     * @param {number} payload.goodsNum - 添加数量
     * @param {Object} [payload.addressInfo] - 地址信息
     * @returns {Promise<null|Object>} 成功返回null，失败返回错误对象
     * 
     * @example
     * const result = await cartStore.add({
     *   goodsId: '123',
     *   skuId: '456', 
     *   goodsNum: 2,
     *   addressInfo: { provinceId: '110000' }
     * })
     * if (!result) {
     *   console.log('商品添加成功')
     * } else {
     *   console.error('添加失败:', result.msg)
     * }
     */
    async add(payload) {
      await this.login()

      const { goodsId, skuId, goodsNum, addressInfo } = payload
      const bizCode = getBizCode('ORDER')
      const [err] = await cart.add({ goodsId, skuId, goodsNum, bizCode, addressInfo })
      if (!err) {
        this.query()
        return null
      } else {
        return err
      }
    },

    /**
     * 更新购物车商品数量
     * 修改购物车中指定商品的数量，包含库存检查和数量验证
     * 
     * @param {Object} payload - 更新参数对象
     * @param {string|number} payload.goodsId - 商品ID
     * @param {string|number} payload.skuId - 商品SKU ID
     * @param {number} payload.goodsNum - 新的商品数量，必须为正整数
     * @returns {Promise<null|Object>} 成功返回null，失败返回错误对象
     * 
     * @example
     * const result = await cartStore.updateGoodsNum({
     *   goodsId: '123',
     *   skuId: '456',
     *   goodsNum: 3
     * })
     * if (!result) {
     *   console.log('数量更新成功')
     * } else {
     *   console.error('更新失败:', result.msg)
     * }
     */
    async updateGoodsNum(payload) {
      await this.login()

      const { goodsId, skuId, goodsNum } = payload
      const goods = getGoods(this.$state, goodsId, skuId)
      if (!goods) return { code: 'FE2002', msg: '当前商品不存在', goodsId, skuId }
      const stock = Number(goods.goods.skuList[0].stock)
      if (goodsNum > stock) {
        return { code: 'FE2001', msg: '最多可购买' + stock + '件', stock }
      }
      if (goodsNum < 1) {
        return { code: 'FE2002', msg: '最少购买 1 件商品', stock }
      }
      const bizCode = getBizCode('ORDER')
      const [err] = await cart.update({ goodsId, skuId, goodsNum, bizCode })
      if (!err) {
        this.updateGoodsMuti([payload])
        return null
      } else {
        return err
      }
    },
    /**
     * 批量更新商品选中状态
     * 批量修改购物车中多个商品的选中状态
     * 
     * @param {Array<Object>} payload - 商品列表
     * @param {string|number} payload[].goodsId - 商品ID
     * @param {string|number} payload[].skuId - 商品SKU ID
     * @param {boolean} payload[].select - 选中状态
     * @returns {Promise<null|Object>} 成功返回null，失败返回错误对象
     * 
     * @example
     * const result = await cartStore.updateGoodsSelectMuti([
     *   { goodsId: '123', skuId: '456', select: true },
     *   { goodsId: '789', skuId: '012', select: false }
     * ])
     * if (!result) {
     *   console.log('选中状态更新成功')
     * }
     */
    async updateGoodsSelectMuti(payload) {
      await this.login()

      const bizCode = getBizCode('ORDER')
      const [err] = await cart.select({ cartGoodsList: JSON.stringify(payload), bizCode })
      if (!err) {
        this.updateGoodsMuti(payload)
        return null
      } else {
        return err
      }
    },

    /**
     * 批量移除商品
     * 从购物车中批量移除指定的商品
     * 
     * @param {Array<Object>} payload - 要移除的商品列表
     * @param {string|number} payload[].goodsId - 商品ID
     * @param {string|number} payload[].skuId - 商品SKU ID
     * @returns {Promise<null|Object>} 成功返回null，失败返回错误对象
     * 
     * @example
     * const result = await cartStore.removeMuti([
     *   { goodsId: '123', skuId: '456' },
     *   { goodsId: '789', skuId: '012' }
     * ])
     * if (!result) {
     *   console.log('商品移除成功')
     * }
     */
    async removeMuti(payload) {
      await this.login()

      const bizCode = getBizCode('ORDER')
      const [err] = await cart.remove({ goodsSkuList: JSON.stringify(payload), bizCode })
      if (!err) {
        this.removeGoodsMuti(payload)
        return null
      } else {
        return err
      }
    },
    /**
     * 移除失效商品
     * 清理购物车中所有失效的商品
     * 
     * @param {Object} payload - 删除参数
     * @param {Array} payload.deleteGoodsList - 要删除的失效商品列表
     * @returns {Promise<null|Object>} 成功返回null，失败返回错误对象
     * 
     * @example
     * const result = await cartStore.removeInvalidGoods({
     *   deleteGoodsList: [{ goodsId: '123', skuId: '456' }]
     * })
     * if (!result) {
     *   console.log('失效商品已清理')
     * } else {
     *   console.error('清理失败:', result.msg)
     * }
     */
    async removeInvalidGoods(payload) {
      await this.login()
      const { deleteGoodsList } = payload
      const bizCode = getBizCode('ORDER')
      const [err] = await cart.removeInvalidGoods({ bizCode, deleteGoodsList })
      if (!err) {
        this.setInvalidList([])
        return null
      } else {
        return err
      }
    },

    /**
     * 删除有效区选中的商品
     * 移除购物车中所有已选中的有效商品
     * 
     * @returns {Promise<null|Object>} 成功返回null，失败返回错误对象
     * 
     * @example
     * const result = await cartStore.removeSelectValidGoods()
     * if (!result) {
     *   console.log('选中商品已移除')
     * } else {
     *   console.error('移除失败:', result.msg)
     * }
     */
    async removeSelectValidGoods() {
      await this.login()
      const removedList = getSelectGoods(this.$state, true).map(goods => {
        return { goodsId: goods.cartGoodsId, skuId: goods.cartSkuId }
      })
      if (removedList.length > 0) {
        return this.removeMuti(removedList)
      }
    },
    /**
     * 购物车有效商品全选反选
     * 智能切换所有有效商品的选中状态，如果存在未选中商品则全选，否则全不选
     * 
     * @returns {Promise<null|Object>} 成功返回null，失败返回错误对象
     * 
     * @example
     * const result = await cartStore.checkedAllValid()
     * if (!result) {
     *   console.log('全选/反选操作成功')
     * }
     */
    async checkedAllValid() {
      await this.login()

      // 查询未选中商品个数
      const unselectCount = getSelectGoods(this.$state, false).length
      // 如果商品存在未选中情况，则执行全选，否则执行反选
      const selectType = unselectCount > 0
      // 需要改变状态的商品列表
      const newPayload = getSelectGoods(this.$state, !selectType).map(goods => {
        return { goodsId: goods.cartGoodsId, skuId: goods.cartSkuId, select: selectType }
      })

      if (newPayload.length === 0) return null
      return this.updateGoodsSelectMuti(newPayload)
    },

    /**
     * 按分组进行全选反选操作
     * 智能切换指定分组下商品的选中状态，如果分组内存在未选中商品则全选，否则全不选
     * 
     * @param {string} payload - 分组名称
     * @returns {Promise<null|Object>} 成功返回null，失败返回错误对象
     * 
     * @example
     * const result = await cartStore.checkedAllByGroupName('京东自营')
     * if (!result) {
     *   console.log('分组全选/反选操作成功')
     * }
     */
    async checkedAllByGroupName(payload) {
      await this.login()

      // 查询未选中商品个数
      const unselectCount = getSelectGoods(this.$state, false, payload).length
      // 如果商品存在未选中情况，则执行全选，否则执行反选
      const selectType = unselectCount > 0
      // 需要改变状态的商品列表
      const newPayload = getSelectGoods(this.$state, !selectType, payload).map(goods => {
        return { goodsId: goods.cartGoodsId, skuId: goods.cartSkuId, select: selectType }
      })

      if (newPayload.length === 0) return null
      return this.updateGoodsSelectMuti(newPayload)
    },
    /**
     * 检查单个SKU库存状态
     * 通过SKU信息计算库存状态，无需关注登录状态
     * 
     * @param {Object} payload - 检查参数
     * @param {Object} payload.sku - SKU信息对象
     * @param {string|number} payload.sku.stock - 库存数量
     * @param {string} payload.sku.supplierCode - 供应商代码
     * @param {number} payload.skuNum - 需要的商品数量
     * @returns {Promise<{flag: boolean, num: number|null}>} 库存检查结果
     * @returns {boolean} returns.flag - true表示库存充足，false表示库存不足
     * @returns {number|null} returns.num - 具体库存数量，null表示数量未知但充裕
     * 
     * @example
     * const stockResult = await cartStore.checkStock({
     *   sku: { stock: '10', supplierCode: 'self' },
     *   skuNum: 2
     * })
     * if (stockResult.flag) {
     *   console.log('库存充足，可购买数量:', stockResult.num)
     * } else {
     *   console.log('库存不足，当前库存:', stockResult.num)
     * }
     */
    async checkStock(payload) {
      const { sku, skuNum } = payload
      const result = { flag: true, num: null }

      // sku.stock=0 表示无货（业管配置）
      if (String(sku.stock) === '0') {
        result.flag = false
        result.num = 0
        return result
      }
      // 无供货商skuId则认为无货（20220523，后端郝孝虎说这个字段不再使用了，by LIJINGCHEN）
      // if (!sku.supplierSkuId) {
      //   result.flag = false
      //   result.num = 0
      //   return result
      // }
      // 非京东供货商返回业管配置的商品库存
      if (sku.supplierCode !== 'jd') {
        if (!sku.stock) {
          // 库存未知（可能是存量数据），认为库存充足
          result.flag = true
          result.num = null
        } else {
          // 有具体库存数量
          result.flag = Number(sku.stock) >= skuNum
          result.num = Number(sku.stock)
        }
        return result
      }

      // 走到此处，是京东逻辑，由于目前没有京东商品了，故直接算作库存为0
      result.flag = false
      result.num = 0
      return result
    }
  }
})
