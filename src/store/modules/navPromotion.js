/**
 * @fileoverview 导航推广状态管理模块
 * @description 管理导航栏推广图标的显示和数据
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { defineStore } from 'pinia'
import { getIconInfo } from '@api/interface/bannerIcon.js'

/**
 * 导航推广状态管理Store
 * @description 管理导航栏推广图标的显示和数据
 */
export const useNavPromotionStore = defineStore('navPromotion', {
  /**
   * 状态定义
   * @returns {Object} 导航推广状态对象
   */
  state: () => ({
    /**
     * 菜单项信息
     * @type {Object|null}
     * @description 推广菜单项的详细信息，包含标题、路径、图片等
     */
    menuItem: null
  }),
  
  /**
   * 操作方法
   */
  actions: {
    /**
     * 设置菜单项
     * @description 设置推广菜单项的信息
     * @param {Object} payload - 菜单项信息对象
     * @param {string} payload.title - 菜单标题
     * @param {string} payload.path - 菜单路径
     * @param {string} payload.img - 菜单图片URL
     * @param {string} payload.activeImg - 激活状态图片URL
     * @param {string} payload.className - CSS类名
     * 
     * @example
     * // 设置菜单项
     * navPromotionStore.setMenuItem({
     *   title: '推广活动',
     *   path: '/promotion',
     *   img: 'https://example.com/icon.png',
     *   activeImg: '',
     *   className: 'promotion-icon'
     * })
     */
    setMenuItem(payload) {
      this.menuItem = payload
    },
    
    /**
     * 查询推广菜单信息
     * @description 根据业务代码查询推广菜单信息，如果已有缓存则直接返回
     * @param {Object} params - 查询参数
     * @param {string} params.bizCode - 业务代码
     * @returns {Promise<Object>} 菜单项信息对象
     * 
     * @example
     * // 查询推广菜单
     * const menuItem = await navPromotionStore.query({ bizCode: 'CCMS001' })
     * console.log(menuItem.path) // 菜单路径
     */
    async query({ bizCode }) {
      if (this.menuItem) return this.menuItem
      const [, json] = await getIconInfo({ bizCode, showPage: 3 })
      let menuItem = {}
      if (json && json.length > 0) {
        const menuList = json[0]
        menuItem = {
          title: '',
          path: menuList.url,
          img: menuList.imgUrl,
          activeImg: '',
          className: 'promotion-icon'
        }
      }
      this.setMenuItem(menuItem)
      return menuItem
    }
  }
})
