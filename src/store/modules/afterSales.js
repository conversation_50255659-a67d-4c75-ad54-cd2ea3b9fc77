/**
 * @fileoverview 售后服务状态管理模块
 * @description 管理售后服务相关的状态信息，包括申请类型、售后状态、订单信息等
 * <AUTHOR> Team
 * @since 1.0.0
 */
import { defineStore } from 'pinia'

/**
 * 售后服务状态管理store
 * @description 管理售后服务的各种状态信息，提供状态更新和获取功能
 * @returns {Object} Pinia store实例
 * 
 * @example
 * // 在组件中使用
 * import { useAfterSalesStore } from '@/store/modules/afterSales'
 * 
 * const afterSalesStore = useAfterSalesStore()
 * 
 * // 更新售后信息
 * afterSalesStore.updateAfterSalesInfo({
 *   applyType: 'refund',
 *   afterSaleState: 'processing',
 *   bizOrderId: 'ORDER123'
 * })
 * 
 * // 获取售后信息
 * const info = afterSalesStore.getAfterSalesInfo
 */
export const useAfterSalesStore = defineStore('afterSales', {
  /**
   * 状态定义
   * @returns {Object} 状态对象
   * @returns {string} return.applyType - 申请类型（如退款、换货等）
   * @returns {string} return.afterSaleState - 售后状态
   * @returns {string} return.bizOrderId - 业务订单ID
   * @returns {string} return.bizCode - 业务代码
   * @returns {string} return.orderState - 订单状态
   */
  state: () => ({
    /** 申请类型 */
    applyType: '',
    /** 售后状态 */
    afterSaleState: '',
    /** 业务订单ID */
    bizOrderId: '',
    /** 业务代码 */
    bizCode: '',
    /** 订单状态 */
    orderState: ''
  }),

  /**
   * 动作方法定义
   */
  actions: {
    /**
     * 更新售后服务信息
     * @description 批量更新售后服务相关信息，只更新传入的字段
     * @param {Object} payload - 要更新的售后信息
     * @param {string} [payload.applyType] - 申请类型
     * @param {string} [payload.afterSaleState] - 售后状态
     * @param {string} [payload.bizOrderId] - 业务订单ID
     * @param {string} [payload.bizCode] - 业务代码
     * @param {string} [payload.orderState] - 订单状态
     * 
     * @example
     * // 更新部分信息
     * updateAfterSalesInfo({
     *   applyType: 'refund',
     *   afterSaleState: 'approved'
     * })
     * 
     * // 更新所有信息
     * updateAfterSalesInfo({
     *   applyType: 'exchange',
     *   afterSaleState: 'processing',
     *   bizOrderId: 'ORDER123',
     *   bizCode: 'BIZ001',
     *   orderState: 'pending'
     * })
     */
    updateAfterSalesInfo(payload) {
      // 只更新传入的字段，避免覆盖未传入的字段
      if (payload.applyType !== undefined) this.applyType = payload.applyType
      if (payload.afterSaleState !== undefined) this.afterSaleState = payload.afterSaleState
      if (payload.bizOrderId !== undefined) this.bizOrderId = payload.bizOrderId
      if (payload.bizCode !== undefined) this.bizCode = payload.bizCode
      if (payload.orderState !== undefined) this.orderState = payload.orderState
    }
  },

  /**
   * 计算属性定义
   */
  getters: {
    /**
     * 获取完整的售后信息
     * @description 返回当前store中的所有售后信息
     * @param {Object} state - 当前状态
     * @returns {Object} 完整的售后信息对象
     * 
     * @example
     * // 获取售后信息
     * const afterSalesInfo = afterSalesStore.getAfterSalesInfo
     * console.log(afterSalesInfo.applyType) // 申请类型
     * console.log(afterSalesInfo.afterSaleState) // 售后状态
     */
    getAfterSalesInfo: (state) => state
  }
})
