/**
 * @fileoverview 京东自营相关状态管理
 * @description 管理京东自营页面的标签栏状态
 * <AUTHOR> Coding
 * @since 2024
 */

import { defineStore } from 'pinia'

/**
 * 京东自营状态管理
 * @description 用于管理京东自营页面的标签栏状态
 * @returns {Object} Pinia store实例
 */
export const useJdzyStore = defineStore('jdzy', {
  /**
   * 状态定义
   * @returns {Object} 状态对象
   */
  state: () => ({
    /**
     * 京东标签栏当前选中项
     * @type {string}
     * @default '0'
     * @description 标识当前选中的京东标签栏项目，'0'表示默认选中第一个标签
     */
    JDtabbar: '0'
  }),
  
  actions: {
    /**
     * 切换京东标签栏
     * @description 更新京东标签栏的选中状态
     * @param {string} payload - 新的标签栏值
     * 
     * @example
     * // 切换到第二个标签
     * jdzyStore.changeJDtabbar('1')
     * 
     * // 切换到第一个标签
     * jdzyStore.changeJDtabbar('0')
     */
    changeJDtabbar(payload) {
      this.JDtabbar = payload
    }
  }
})