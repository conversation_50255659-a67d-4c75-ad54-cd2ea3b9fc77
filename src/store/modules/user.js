/**
 * @fileoverview 用户状态管理模块
 * @description 管理用户登录状态、地址信息、用户ID等相关数据和操作
 * <AUTHOR> Team
 * @since 1.0.0
 */

import { defineStore } from 'pinia'
import { urlAppend, useLogin, log } from 'commonkit'
import { queryUserAddrList, queryUserDefaultAddr } from '@api/interface/address.js'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz, curTempAddrInfo, loginType } from '@utils/storage.js'

/**
 * 用户状态管理Store
 * @description 管理用户登录状态、地址信息、用户ID等相关数据和操作
 */
export const useUserStore = defineStore('user', {
  /**
   * 状态定义
   * @returns {Object} 用户状态对象
   */
  state: () => ({
    /**
     * 用户登录状态
     * @type {boolean|null}
     * @description null-未查询，true-已登录，false-未登录
     */
    isLogin: null,
    
    /**
     * 用户CIF ID
     * @type {string|null}
     * @description 用户的唯一标识ID
     */
    cifUserId: null,
    
    /**
     * 用户临时选择的地址
     * @type {Array}
     * @description 数组形式，Vant-Cascader 数据结构
     * @example [{ areaId: '1', areaName: '北京' }, { areaId: '72', areaName: '朝阳区' }]
     */
    addressInfoTemp: curTempAddrInfo.get() || [],
    
    /**
     * 用户的地址信息
     * @type {Object|null}
     * @description 用户当前选中的地址详细信息
     */
    addressInfo: null,
    
    /**
     * 用户地址列表
     * @type {Array|null}
     * @description 用户保存的所有地址列表
     */
    addressList: null,
    
    /**
     * 默认地址信息
     * @type {Object}
     * @description 用户没有地址情况下，默认的地址（北京-朝阳区-管庄地区）
     */
    addressInfoDefault: {
      provinceId: '1',
      provinceName: '北京',
      cityId: '72',
      cityName: '朝阳区',
      countyId: '4137',
      countyName: '管庄地区',
      townId: '0',
      townName: ''
    }
  }),
  /**
   * 计算属性
   */
  getters: {
    /**
     * 获取当前地址信息
     * @description 根据优先级返回地址信息：临时地址 > 用户真实地址 > 默认地址
     * @returns {Object} 当前地址信息对象
     * @property {number} type - 地址类型：1-用户真实地址，2-默认地址，3-临时地址
     * @property {string} addressId - 地址ID
     * @property {boolean} isDefault - 是否为默认地址
     * @property {string} provinceId - 省份ID
     * @property {string} provinceName - 省份名称
     * @property {string} cityId - 城市ID
     * @property {string} cityName - 城市名称
     * @property {string} countyId - 区县ID
     * @property {string} countyName - 区县名称
     * @property {string} townId - 乡镇ID
     * @property {string} townName - 乡镇名称
     * @property {string} addrDetail - 详细地址
     * @property {string} recName - 收货人姓名
     * @property {string} recPhone - 收货人电话
     * 
     * @example
     * // 获取当前地址信息
     * const currentAddr = userStore.curAddressInfo
     * console.log(currentAddr.type) // 1, 2, 或 3
     */
    curAddressInfo() {
      const addrInfo = this.addressInfo
      const addrInfoDefault = this.addressInfoDefault
      const addrInfoTemp = this.addressInfoTemp
      if (addrInfoTemp[0]) {
        // 存在临时地址，直接返回临时地址数据
        const province = addrInfoTemp[0]
        const city = addrInfoTemp[1]
        const county = addrInfoTemp[2]
        const town = addrInfoTemp[3]
        return {
          type: 3,
          addressId: '',
          isDefault: false,
          provinceId: province ? province.areaId : '0',
          provinceName: province ? province.areaName : '',
          cityId: city ? city.areaId : '0',
          cityName: city ? city.areaName : '',
          countyId: county ? county.areaId : '0',
          countyName: county ? county.areaName : '',
          townId: town ? town.areaId : '0',
          townName: town ? town.areaName : '',
          addrDetail: '',
          recName: '',
          recPhone: ''
        }
      } else if (addrInfo?.provinceId) {
        // 存在用户真实地址
        return {
          type: 1,
          addressId: addrInfo.addressId,
          isDefault: addrInfo.isDefault === '1',
          provinceId: addrInfo.provinceId || '0',
          provinceName: addrInfo.provinceName,
          cityId: addrInfo.cityId || '0',
          cityName: addrInfo.cityName,
          countyId: addrInfo.countyId || '0',
          countyName: addrInfo.countyName,
          townId: addrInfo.townId || '0',
          townName: addrInfo.townName,
          addrDetail: addrInfo.addrDetail,
          recName: addrInfo.recName,
          recPhone: addrInfo.recPhone
        }
      } else {
        // 返回默认北京-管庄地址
        return {
          type: 2,
          addressId: '',
          isDefault: false,
          provinceId: addrInfoDefault.provinceId,
          provinceName: addrInfoDefault.provinceName,
          cityId: addrInfoDefault.cityId,
          cityName: addrInfoDefault.cityName,
          countyId: addrInfoDefault.countyId,
          countyName: addrInfoDefault.countyName,
          townId: addrInfoDefault.townId,
          townName: addrInfoDefault.townName,
          addrDetail: '',
          recName: '',
          recPhone: ''
        }
      }
    },
    
    /**
     * 获取地址信息
     * @description 直接返回用户的地址信息
     * @returns {Object|null} 用户地址信息对象或null
     * 
     * @example
     * // 获取地址信息
     * const addressInfo = userStore.getAddressInfo
     */
    getAddressInfo() {
      return this.addressInfo
    }
  },
  /**
   * 操作方法
   */
  actions: {
    /**
     * 设置登录状态
     * @description 更新用户的登录状态
     * @param {boolean|null} payload - 登录状态：true-已登录，false-未登录，null-未查询
     * 
     * @example
     * // 设置为已登录
     * userStore.setLoginStatus(true)
     * // 设置为未登录
     * userStore.setLoginStatus(false)
     */
    setLoginStatus(payload) {
      this.isLogin = payload
    },
    
    /**
     * 设置默认地址
     * @description 设置用户的默认地址信息
     * @param {Object|null} payload - 地址信息对象
     * 
     * @example
     * // 设置默认地址
     * userStore.setDefaultAddr({
     *   addressId: '123',
     *   provinceName: '北京',
     *   cityName: '朝阳区'
     * })
     */
    setDefaultAddr(payload) {
      if (!payload) return
      this.addressInfo = payload
    },
    
    /**
     * 设置临时地址
     * @description 设置用户临时选择的地址，同时保存到本地存储
     * @param {Array} payload - 临时地址数组（Vant-Cascader 数据结构）
     * 
     * @example
     * // 设置临时地址
     * userStore.setTempAddr([
     *   { areaId: '1', areaName: '北京' },
     *   { areaId: '72', areaName: '朝阳区' }
     * ])
     */
    setTempAddr(payload) {
      this.addressInfoTemp = payload || []
      curTempAddrInfo.set(payload)
    },
    
    /**
     * 设置地址列表
     * @description 设置用户的地址列表
     * @param {Array|null} payload - 地址列表数组
     * 
     * @example
     * // 设置地址列表
     * userStore.setAddrList([
     *   { addressId: '1', provinceName: '北京' },
     *   { addressId: '2', provinceName: '上海' }
     * ])
     */
    setAddrList(payload) {
      if (!payload) return
      this.addressList = payload
    },
    
    /**
     * 设置用户CIF ID
     * @description 设置用户的唯一标识ID
     * @param {string|null} payload - 用户CIF ID
     * 
     * @example
     * // 设置用户ID
     * userStore.setCifUserId('12345678')
     */
    setCifUserId(payload) {
      this.cifUserId = payload
    },
    /**
     * 查询默认地址
     * @description 查询用户的默认地址信息，需要先确认登录状态
     * @param {Object} options - 查询选项
     * @param {boolean} [options.force=false] - 是否强制查询，忽略缓存
     * @returns {Promise<void>}
     * 
     * @example
     * // 查询默认地址
     * await userStore.queryDefaultAddr()
     * // 强制查询默认地址
     * await userStore.queryDefaultAddr({ force: true })
     */
    async queryDefaultAddr({ force = false } = {}) {
      log('user.js queryDefaultAddr 查询登录')
      await this.queryLoginStatus()
      if (!this.isLogin) return // 未登录
      if (!force && this.addressInfo?.provinceId) return // 已经查询过默认地址
      const [err, json] = await queryUserDefaultAddr()
      if (!err) {
        this.setDefaultAddr(json || {})
      }
    },
    
    /**
     * 查询地址列表
     * @description 查询用户的所有地址列表，需要先确认登录状态
     * @param {Object} options - 查询选项
     * @param {boolean} [options.force=false] - 是否强制查询，忽略缓存
     * @returns {Promise<void>}
     * 
     * @example
     * // 查询地址列表
     * await userStore.queryAddrList()
     * // 强制查询地址列表
     * await userStore.queryAddrList({ force: true })
     */
    async queryAddrList({ force = false } = {}) {
      log('user.js queryAddrList 查询登录')
      await this.queryLoginStatus()
      if (!this.isLogin) return // 未登录
      if (!force && this.addressList) return // 已经查询过默认地址
      const [err, json] = await queryUserAddrList()
      if (!err) {
        this.setAddrList(json || [])
      }
    },
    /**
     * 查询登录状态
     * @description 查询用户的登录状态，如果已有明确结果则不重复查询
     * @returns {Promise<void>}
     * 
     * @example
     * // 查询登录状态
     * await userStore.queryLoginStatus()
     */
    async queryLoginStatus() {
      if (typeof this.isLogin === 'boolean') {
        // 已经有了明确结果，不在查询了
        log('user.js queryLoginStatus 当前已存在登录状态', this.isLogin)
        return
      }
      const [loginState, queryStatus] = useLogin()
      // 查询登录状态
      await queryStatus({ useHistoryReplace: true, loginType: loginType.get() || '0' })
      // loginState.status 用户登录状态，0-未检查，-1-登录错误，1-未登录，2-已登录
      const isLogin = loginState.status === 2
      const cifId = loginState.cifId
      log('user.js queryLoginStatus 查询登录状态', isLogin)
      this.setLoginStatus(isLogin)
      this.setCifUserId(cifId)
    },
    
    /**
     * 用户登录
     * @description 处理用户登录逻辑，如果已登录可选择刷新页面，未登录则跳转到登录页面
     * @param {Object} payload - 登录参数
     * @param {boolean} [payload.reload=true] - 已登录时是否刷新页面
     * @returns {Promise<boolean>} 登录是否成功
     * 
     * @example
     * // 登录并刷新页面
     * const success = await userStore.login()
     * // 登录但不刷新页面
     * const success = await userStore.login({ reload: false })
     */
    async login(payload = {}) {
      const reload = typeof payload.reload === 'boolean' ? payload.reload : true
      if (this.isLogin) {
        // 已登录，直接刷新防止页面没处理状态
        if (reload) window.location.reload()
        return true
      }

      const [, , toLogin] = useLogin()
      const url = window.location.href
      const callbackUrl = urlAppend(url, { distri_biz_code: getBizCode(), biz_channel_code: curChannelBiz.get() })
      return new Promise((resolve) => {
        toLogin({
          callbackUrl,
          callback: (res) => {
            resolve(res === 2)
          }
        }, { useHistoryReplace: true, loginType: loginType.get() || '0' })
      })
    }
  }
})
