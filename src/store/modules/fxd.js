/**
 * @fileoverview 分销（分享赚钱）状态管理模块
 * @description 管理分销业务相关的状态信息，包括销售员信息、收益数据、订单列表等
 * <AUTHOR> Team
 * @since 1.0.0
 */
import { defineStore } from 'pinia'
import { queryIncomeInfo, queryOrderList, checkSignature } from '@api/interface/fxd.js'
import { curDeveloperId } from '@utils/storage.js'

/** 会话存储实例 */
const storage = window.sessionStorage

/**
 * 分销业务状态管理store
 * @description 管理分销相关的业务逻辑，包括销售员管理、收益查询、订单处理等功能
 * @returns {Object} Pinia store实例
 * 
 * @example
 * // 在组件中使用
 * import { useFxdStore } from '@/store/modules/fxd'
 * 
 * const fxdStore = useFxdStore()
 * 
 * // 查询收益信息
 * await fxdStore.queryIncomeInfo({ fxdtoken: 'token123' })
 * 
 * // 查询订单列表
 * await fxdStore.queryOrderList({ orderType: '1' })
 */
export const useFxdStore = defineStore('fxd', {
  /**
   * 状态定义
   * @returns {Object} 状态对象
   * @returns {string} return.salesmanId - 销售员ID（能人ID），用于查看能人收益和订单
   * @returns {string} return.developerId - 发展人ID，能人也属于发展人，用于用户购买商品时数据统计
   * @returns {string} return.distribizCode - 分销业务代码
   * @returns {Array<string>} return.distribizCodeList - 支持的分销业务代码列表
   * @returns {Object} return.info - 收益信息
   * @returns {string} return.info.expect - 预估收益（元）
   * @returns {string} return.info.real - 实际收益（元）
   * @returns {string} return.info.count - 收益订单数量
   * @returns {Array} return.orderList - 订单列表
   * @returns {boolean} return.isValid - 是否有效
   */
  state: () => ({
    /** 销售员ID（能人ID） */
    salesmanId: '',
    /** 发展人ID */
    developerId: '',
    /** 分销业务代码 */
    distribizCode: '',
    /** 支持的分销业务代码列表 */
    distribizCodeList: ['ziying', 'st-dbgj'],
    /** 收益信息 */
    info: {
      /** 预估收益（元） */
      expect: '0.00',
      /** 实际收益（元） */
      real: '0.00',
      /** 收益订单数量 */
      count: '0'
    },
    /** 订单列表 */
    orderList: [],
    /** 是否有效 */
    isValid: false
  }),
  /**
   * 动作方法定义
   */
  actions: {
    /**
     * 设置销售员信息
     * @description 设置销售员ID和分销业务代码，并保存到会话存储
     * @param {Object} payload - 销售员信息
     * @param {string} payload.salesmanId - 销售员ID
     * @param {string} payload.distribizCode - 分销业务代码
     * 
     * @example
     * // 设置销售员信息
     * fxdStore.setSalesmanId({
     *   salesmanId: 'salesman123',
     *   distribizCode: 'ziying'
     * })
     */
    setSalesmanId(payload) {
      this.salesmanId = payload.salesmanId
      this.distribizCode = payload.distribizCode
      // 保存到会话存储
      storage.setItem('salesmanId', payload.salesmanId)
      storage.setItem('distribizCode', payload.distribizCode)
    },

    /**
     * 设置收益信息
     * @description 更新收益相关数据，将分为单位的金额转换为元
     * @param {Object} payload - 收益信息
     * @param {number} [payload.estimatedIncome] - 预估收益（分）
     * @param {number} [payload.payedIncome] - 已支付收益（分）
     * @param {number} [payload.estimatedIncomeCount] - 预估收益订单数量
     * 
     * @example
     * // 设置收益信息
     * fxdStore.setIncomeInfo({
     *   estimatedIncome: 12500, // 125.00元
     *   payedIncome: 10000,     // 100.00元
     *   estimatedIncomeCount: 5
     * })
     */
    setIncomeInfo(payload) {
      // 将分转换为元，保留两位小数
      payload.estimatedIncome && (this.info.expect = (payload.estimatedIncome / 100).toFixed(2))
      payload.payedIncome && (this.info.real = (payload.payedIncome / 100).toFixed(2))
      payload.estimatedIncomeCount && (this.info.count = payload.estimatedIncomeCount)
    },
    /**
     * 设置订单列表
     * @description 将后台接口返回的订单数据转换为前端页面所需的格式
     * @param {Array} payload - 后台返回的订单数据
     * 
     * @example
     * // 设置订单列表
     * fxdStore.setOrderList([
     *   {
     *     orderId: '123456',
     *     grandResultBz: '1',
     *     supplierOrder: [{
     *       supplierOrderId: 'SUP001',
     *       supplierOrderState: '1',
     *       price: 10000,
     *       commission: 500,
     *       skuNumInfo: [...]
     *     }]
     *   }
     * ])
     */
    setOrderList(payload) {
      // 数据转换，将后台接口数据转换成前端页面数据
      const list = payload.map(item => {
        let count = 0
        let supplierName = ''
        let totalAmt = 0
        // 目前一个主订单下只有一个供应商订单，因此取第一个数据即可
        const supplier = item.supplierOrder[0]

        if (!supplier) {
          return {}
        }
        
        const _item = {
          parentId: item.orderId,
          // 佣金是否发放：0未申请 1申请成功 2已发放
          grandResultBz: item.grandResultBz,
          id: supplier.supplierOrderId,
          key: `${item.orderId}_${supplier.supplierOrderId}`,
          name: supplierName,
          // 订单状态：1 待收货；2 已签收；3 已取消
          status: supplier.supplierOrderState,
          totalCount: '',
          totalAmt: supplier.price / 100,
          totalIncome: supplier.commission / 100,
          list: (supplier.skuNumInfo || []).map(item => {
            count += Number(item.skuNum)
            if (item.sku.supplierName) supplierName = item.sku.supplierName
            const promotion = item.sku.skuPromotionList && item.sku.skuPromotionList[0]

            // 计算总金额
            if (promotion) {
              totalAmt += (promotion.promotionPrice / 100) * item.skuNum
            } else {
              totalAmt += (item.sku.price / 100) * item.skuNum
            }

            // 确定显示价格
            let price = item.sku.price / 100
            if (item.loanOrder && item.loanOrder.loanProduct && item.loanOrder.loanProduct.loanAmount) {
              // 花呗支付，展示借款金额
              price = item.loanOrder.loanProduct.loanAmount / 100
            } else if (promotion && promotion.promotionPrice) {
              // 促销产品，展示促销价格
              price = promotion.promotionPrice / 100
            }

            return {
              id: item.sku.skuId,
              img: item.sku.detailImageUrl,
              name: item.sku.name,
              price,
              count: item.skuNum,
              // 后台不返回是否是推广商品的状态，目前一个订单只有一个商品，
              // 因此根据订单的佣金 commission 来判断
              type: Number(supplier.commission) === 0 ? '0' : '1'
            }
          })
        }

        // 更新总金额
        if (totalAmt) _item.totalAmt = totalAmt
        if (item.loanOrder && item.loanOrder.loanProduct && item.loanOrder.loanProduct.loanAmount) {
          // 花呗订单需要展示借款金额
          _item.totalAmt = item.loanOrder.loanProduct.loanAmount / 100
        }
        
        // 处理特殊状态订单
        if (_item.status === '2' || _item.status === '10') {
          // 已取消（未支付）、已退款订单，需要将佣金收益重置为 0
          _item.totalIncome = 0
        }
        
        _item.name = supplierName
        _item.totalCount = count

        return _item
      }).filter(item => !!item.id)

      // 冻结数组，防止意外修改
      this.orderList = Object.freeze(list)
    },
    /**
     * 设置开发者ID
     * @description 设置当前开发者的唯一标识符
     * @param {Object} payload - 包含开发者ID的对象
     * @param {string} payload.developerId - 开发者ID
     * 
     * @example
     * // 设置开发者ID
     * fxdStore.setDeveloperId({ developerId: 'DEV123456' })
     */
    setDeveloperId(payload) {
      this.developerId = payload.developerId || ''
      curDeveloperId.set(this.developerId)
    },
    /**
     * 查询销售员ID
     * @description 根据fxdtoken查询销售员ID，如果没有token则从缓存读取
     * @param {Object} [payload] - 查询参数
     * @param {string} [payload.fxdtoken] - 分销token
     * @param {string} [payload.distribizCode] - 分销业务代码
     * @returns {Promise<void>} 无返回值的Promise
     * 
     * @example
     * // 使用token查询
     * await fxdStore.querySalesmanId({ fxdtoken: 'token123', distribizCode: 'ziying' })
     * 
     * // 从缓存读取
     * await fxdStore.querySalesmanId()
     */
    async querySalesmanId(payload) {
      let salesmanId = ''
      let distribizCode = ''

      if (payload && payload.fxdtoken) {
        distribizCode = payload.distribizCode || ''
        const [, res] = await checkSignature({
          fxdtoken: payload.fxdtoken,
          distribizCode: distribizCode || 'st-dbgj'
        })

        if (res && res.code === '0000') {
          salesmanId = res.data
        }
        // mock
        // salesmanId = 'fxd248dc6433f65ffa25c2e79f6bf4631f3'
      } else {
        salesmanId = storage.getItem('salesmanId') || ''
        distribizCode = storage.getItem('distribizCode') || ''
      }

      this.setSalesmanId({ salesmanId, distribizCode })
    },
    /**
     * 查询收益信息
     * @description 根据销售员ID异步获取收益信息并更新到状态中
     * @param {Object} payload - 查询参数
     * @param {string} [payload.fxdtoken] - 分销token
     * @param {string} [payload.distribizCode] - 分销业务代码
     * @returns {Promise<void>} 无返回值的Promise
     * 
     * @example
     * // 查询收益信息
     * await fxdStore.queryIncomeInfo({ fxdtoken: 'token123' })
     */
    async queryIncomeInfo(payload) {
      await this.querySalesmanId({ fxdtoken: payload.fxdtoken, distribizCode: payload.distribizCode })

      if (!this.salesmanId) return

      if (this.distribizCode) {
        // 指定了 distribizCode
        const [err, json] = await queryIncomeInfo({ shopCode: this.salesmanId, distribizCode: this.distribizCode })

        if (!err) {
          this.setIncomeInfo(json || {})
        }
      } else {
        // 未指定 distribizCode，后台不支持查询全部，只能遍历查询
        const dataLists = await Promise.all(this.distribizCodeList.map(code => {
          return queryIncomeInfo({
            shopCode: this.salesmanId,
            distribizCode: code
          }).then(res => {
            const [err, json] = res
            return !err && json ? json : {}
          })
        }))

        const data = dataLists.reduce((acc, curr) => {
          return {
            estimatedIncome: Number(acc.estimatedIncome) + Number(curr.estimatedIncome),
            payedIncome: Number(acc.payedIncome) + Number(curr.payedIncome),
            estimatedIncomeCount: Number(acc.estimatedIncomeCount) + Number(curr.estimatedIncomeCount)
          }
        })
        this.setIncomeInfo(data)
      }
    },
    /**
     * 查询订单列表
     * @description 根据分销token查询销售员ID，然后获取订单列表并更新到状态中
     * @param {Object} payload - 查询参数
     * @param {string} [payload.fxdtoken] - 分销token
     * @param {string} [payload.distribizCode] - 分销业务代码
     * @param {string} [payload.orderType] - 订单类型
     * @returns {Promise<void>} 无返回值的Promise
     * 
     * @example
     * // 查询订单列表
     * await fxdStore.queryOrderList({ fxdtoken: 'token123', distribizCode: 'ziying', orderType: '1' })
     */
    async queryOrderList(payload) {
      await this.querySalesmanId()

      if (!this.salesmanId) return

      if (this.distribizCode) {
        // 指定了 distribizCode
        const [err, json] = await queryOrderList(
          { shopCode: this.salesmanId, orderState: payload.orderType, distribizCode: this.distribizCode })

        if (!err && json) {
          this.setOrderList(json || [])
        }
      } else {
        // 未指定 distribizCode，后台不支持查询全部，只能遍历查询
        const dataLists = await Promise.all(this.distribizCodeList.map(code => {
          return queryOrderList({
            shopCode: this.salesmanId,
            orderState: payload.orderType,
            distribizCode: code
          }).then(res => {
            const [err, json] = res
            return !err && json ? json : []
          })
        }))

        const list = dataLists.reduce((acc, curr) => {
          return acc.concat(curr)
        }, []).sort((prev, next) => next.orderId - prev.orderId)
        this.setOrderList(list)
      }
    },
    /**
     * 获取开发者ID
     * @description 异步获取开发者ID并更新到状态中
     * @returns {Promise<void>} 无返回值的Promise
     * 
     * @example
     * // 获取开发者ID
     * await fxdStore.getDeveloperId()
     */
    async getDeveloperId() {
      const cache = curDeveloperId.get()
      if (cache) {
        this.setDeveloperId({ developerId: cache })
      }
    }
  }
})
