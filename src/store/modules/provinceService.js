/**
 * @fileoverview 省份服务商管理Store
 * 
 * 该模块负责管理省份和服务商的选择状态，提供省份列表、服务商列表的管理功能。
 * 支持数据缓存，用户选择的省份和服务商会自动保存到sessionStorage中。
 * 
 * 主要功能：
 * - 省份选择和管理
 * - 服务商选择和管理
 * - 选择状态的缓存和恢复
 * - 服务商列表的动态获取
 * 
 * @module ProvinceServiceStore
 * @requires pinia - 状态管理库
 * @requires commonkit - 通用工具库（storage功能）
 * @requires @/api/interface/zq - 服务商接口
 * 
 * @example
 * // 基本使用
 * import { useProvinceServiceStore } from '@/store/modules/provinceService'
 * 
 * const provinceServiceStore = useProvinceServiceStore()
 * 
 * // 选择省份
 * provinceServiceStore.selectProvince('1') // 选择北京
 * 
 * // 获取服务商列表
 * await provinceServiceStore.fetchServiceList()
 * 
 * // 选择服务商
 * provinceServiceStore.selectService('isv001')
 * 
 * // 重置选择
 * provinceServiceStore.resetSelection()
 */

import { defineStore } from 'pinia'
import { storage } from 'commonkit'
import { zqAllSupplierList } from '@/api/interface/zq'

/**
 * 创建sessionStorage缓存实例
 * 用于持久化用户的省份和服务商选择状态
 */
const selectedAreaIdCache = storage('PS_CCMS_SELECTED_AREA_ID', false)
const selectedIsvIdCache = storage('PS_CCMS_SELECTED_ISV_ID', false)

/**
 * 省份服务商管理Store
 * 管理省份和服务商的选择状态，提供相关的查询和操作功能
 * 
 * @returns {Object} Pinia store实例
 */
export const useProvinceServiceStore = defineStore('provinceService', {
  /**
   * Store状态定义
   * 
   * @returns {Object} 状态对象
   * @property {string} selectedAreaId - 当前选中的省份ID，从缓存中恢复
   * @property {string} selectedIsvId - 当前选中的服务商ID，从缓存中恢复
   * @property {Array<{areaId: string, areaName: string}>} provinceList - 省份列表，包含全国31个省市自治区
   * @property {Array<Object>} serviceList - 服务商列表，动态从接口获取
   * @property {boolean} loading - 服务商列表加载状态
   */
  state: () => ({
    /** @type {string} 当前选中的省份ID */
    selectedAreaId: selectedAreaIdCache.get() || '',
    /** @type {string} 当前选中的服务商ID */
    selectedIsvId: selectedIsvIdCache.get() || '',
    /** @type {Array<{areaId: string, areaName: string}>} 省份列表 */
    provinceList: [
      { areaId: '1', areaName: '北京' },
      { areaId: '2', areaName: '上海' },
      { areaId: '3', areaName: '天津' },
      { areaId: '4', areaName: '重庆' },
      { areaId: '5', areaName: '河北' },
      { areaId: '6', areaName: '山西' },
      { areaId: '7', areaName: '河南' },
      { areaId: '8', areaName: '辽宁' },
      { areaId: '9', areaName: '吉林' },
      { areaId: '10', areaName: '黑龙江' },
      { areaId: '11', areaName: '内蒙古' },
      { areaId: '12', areaName: '江苏' },
      { areaId: '13', areaName: '山东' },
      { areaId: '14', areaName: '安徽' },
      { areaId: '15', areaName: '浙江' },
      { areaId: '16', areaName: '福建' },
      { areaId: '17', areaName: '湖北' },
      { areaId: '18', areaName: '湖南' },
      { areaId: '19', areaName: '广东' },
      { areaId: '20', areaName: '广西' },
      { areaId: '21', areaName: '江西' },
      { areaId: '22', areaName: '四川' },
      { areaId: '23', areaName: '海南' },
      { areaId: '24', areaName: '贵州' },
      { areaId: '25', areaName: '云南' },
      { areaId: '26', areaName: '西藏' },
      { areaId: '27', areaName: '陕西' },
      { areaId: '28', areaName: '甘肃' },
      { areaId: '29', areaName: '青海' },
      { areaId: '30', areaName: '宁夏' },
      { areaId: '31', areaName: '新疆' }
    ],
    /** @type {Array<Object>} 服务商列表，每次都从接口获取 */
    serviceList: [],
    /** @type {boolean} 服务商列表加载状态 */
    loading: false
  }),
  /**
   * 计算属性定义
   * 提供基于状态的派生数据
   */
  getters: {
    /**
     * 获取当前选中省份的名称
     * 根据selectedAreaId从provinceList中查找对应的省份名称
     * 
     * @param {Object} state - store状态
     * @returns {string} 省份名称，未选择时返回'全部'
     * 
     * @example
     * const provinceName = provinceServiceStore.selectedProvinceName
     * console.log(provinceName) // '北京' 或 '全部'
     */
    selectedProvinceName: (state) => {
      if (!state.selectedAreaId) return '全部'
      const province = state.provinceList.find(p => p.areaId === state.selectedAreaId)
      return province ? province.areaName : '全部'
    },

    /**
     * 获取当前选中服务商的名称
     * 根据selectedIsvId从serviceList中查找对应的服务商名称
     * 
     * @param {Object} state - store状态
     * @returns {string} 服务商名称，未选择时返回'全部'
     * 
     * @example
     * const serviceName = provinceServiceStore.selectedServiceName
     * console.log(serviceName) // '某某服务商' 或 '全部'
     */
    selectedServiceName: (state) => {
      if (!state.selectedIsvId) return '全部'
      const service = state.serviceList.find(s => s.isvId === state.selectedIsvId)
      return service ? service.isvName : '全部'
    }
  },
  /**
   * 操作方法定义
   * 提供状态修改和业务逻辑处理功能
   */
  actions: {
    /**
     * 选择省份
     * 设置当前选中的省份ID，并同步更新到缓存
     * 
     * @param {string} areaId - 省份ID
     * 
     * @example
     * // 选择北京
     * provinceServiceStore.selectProvince('1')
     * 
     * // 清空选择
     * provinceServiceStore.selectProvince('')
     */
    selectProvince (areaId) {
      this.selectedAreaId = areaId
      // 同步更新缓存
      selectedAreaIdCache.set(areaId)
    },

    /**
     * 选择服务商
     * 设置当前选中的服务商ID，并同步更新到缓存
     * 
     * @param {string} isvId - 服务商ID
     * 
     * @example
     * // 选择某个服务商
     * provinceServiceStore.selectService('isv001')
     * 
     * // 清空选择
     * provinceServiceStore.selectService('')
     */
    selectService (isvId) {
      this.selectedIsvId = isvId
      // 同步更新缓存
      selectedIsvIdCache.set(isvId)
    },

    /**
     * 重置选择
     * 清空所有选择状态，包括省份和服务商，并清空缓存
     * 
     * @example
     * provinceServiceStore.resetSelection()
     * console.log(provinceServiceStore.selectedAreaId) // ''
     * console.log(provinceServiceStore.selectedIsvId) // ''
     */
    resetSelection () {
      this.selectedAreaId = ''
      this.selectedIsvId = ''
      // 同步清空缓存
      selectedAreaIdCache.set('')
      selectedIsvIdCache.set('')
    },

    /**
     * 获取服务商列表
     * 从接口获取服务商列表数据，每次都重新请求最新数据
     * 
     * @param {Object} [params={}] - 请求参数
     * @returns {Promise<Array>} 服务商列表数据
     * 
     * @example
     * // 获取所有服务商
     * const serviceList = await provinceServiceStore.fetchServiceList()
     * console.log(serviceList)
     * 
     * // 带参数获取
     * const filteredList = await provinceServiceStore.fetchServiceList({
     *   areaId: '1',
     *   status: 'active'
     * })
     */
    async fetchServiceList (params = {}) {
      this.loading = true
      try {
        const [error, data] = await zqAllSupplierList(params)
        if (!error && data) {
          this.serviceList = data
          return data
        } else {
          console.error('获取服务商列表失败:', error)
          return []
        }
      } catch (error) {
        console.error('获取服务商列表异常:', error)
        return []
      } finally {
        this.loading = false
      }
    }
  }
})
