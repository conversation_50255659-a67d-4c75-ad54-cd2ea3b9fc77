/**
 * @fileoverview 根状态管理模块
 * @description Pinia根store，管理全局状态，包括贷款产品代码等
 * <AUTHOR> Team
 * @since 1.0.0
 */
import { defineStore } from 'pinia'
import { curLoanCode } from '@utils/storage.js'

/**
 * 根状态管理store
 * @description 管理应用的全局状态，包括贷款产品代码等核心数据
 * @returns {Object} Pinia store实例
 * 
 * @example
 * // 在组件中使用
 * import { useRootStore } from '@/store'
 * 
 * const rootStore = useRootStore()
 * 
 * // 获取贷款产品代码
 * console.log(rootStore.loanProductCode)
 * 
 * // 修改贷款产品代码
 * rootStore.changeLoanProductCode('LOAN001')
 */
export const useRootStore = defineStore('root', {
  /**
   * 状态定义
   * @returns {Object} 状态对象
   * @returns {string|number} return.loanProductCode - 贷款产品代码，默认值为-1（避免默认选中"不分期"选项）
   */
  state: () => ({
    /** 贷款产品代码，-1表示未选择任何贷款产品 */
    loanProductCode: curLoanCode.get() || -1
  }),
  
  /**
   * 动作方法定义
   */
  actions: {
    /**
     * 修改贷款产品代码
     * @description 更新贷款产品代码，同时更新本地存储和状态
     * @param {string|number} payload - 新的贷款产品代码
     * @example
     * // 设置贷款产品代码
     * rootStore.changeLoanProductCode('LOAN001')
     * 
     * // 清除贷款产品代码
     * rootStore.changeLoanProductCode(-1)
     */
    changeLoanProductCode(payload) {
      // 更新本地存储
      curLoanCode.set(payload)
      // 更新状态
      this.loanProductCode = payload
    }
  }
})
