<!--
  政企商城入口组件
  功能：根据用户角色和权限进行路由跳转，处理不同角色的访问控制
  特性：支持多种角色类型（客户、管理员、企业等），权限验证，错误处理
  角色类型：1-客户、2-管理员、4-企业
-->
<template>
  <!-- 无权限提示页面 -->
  <div v-if="noPermission" class="zq-entry">
    <img
      src="@/assets/images/no-p.png"
      alt="无权限"
      class="zq-entry__icon"
      width="200"
      height="200"
      loading="eager"
      decoding="async"
      fetchpriority="high"
    >
    <p class="zq-entry__text">暂无该业务查看权限</p>
  </div>
</template>

<script setup>
import { onBeforeMount, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { woReport } from 'commonkit'
import { queryZqInfo } from '@/utils/zqInfo'

// 权限状态管理
const noPermission = ref(false)

// 路由相关
const route = useRoute()
const router = useRouter()

// 组件挂载前权限检查和路由跳转
onBeforeMount(() => {
  // 获取URL参数中的ISV信息
  const rawIsv = (route.query && route.query.isv) || ''
  const isv = typeof rawIsv === 'string' ? rawIsv : String(rawIsv || '')

  // 获取政企信息和用户角色
  const zqInfo = queryZqInfo() || {}
  const role = String(zqInfo && zqInfo.roleType)
  const list = Array.isArray(zqInfo && zqInfo.isvList) ? zqInfo.isvList : []

  // 根据不同角色类型进行路由跳转
  if (role === '1') {
    // 角色类型1：企业经办人
    if (list.length === 1) {
      // 只有一个ISV时直接跳转
      const only = list[0]
      const targetIsv = typeof only === 'string' ? only : (only && only.isvId) || ''
      router.replace({ path: '/home', query: { isv: targetIsv } })
    } else if (isv && list.some(item => (typeof item === 'string' ? item === isv : item && item.isvId === isv))) {
      // 验证ISV权限后跳转
      router.replace({ path: '/home', query: { isv } })
    } else {
      // 权限验证失败，显示无权限页面并上报错误
      noPermission.value = true
      woReport('政企商城入口 zqInfo 数据异常', JSON.stringify(zqInfo))
    }
  } else if (role === '4') {
    // 角色类型4：白名单，直接跳转首页
    router.replace({ path: '/home', query: { isv } })
  } else if (role === '2') {
    // 角色类型2：客户经理，跳转企业列表管理页
    router.replace('/zq/manager/enterprise-list')
  } else {
    // 未知角色类型，显示无权限页面
    noPermission.value = true
  }
})
</script>

<style lang="less" scoped>
.zq-entry {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #FFFFFF;
}
.zq-entry__icon {
  width: 200px;
  height: 200px;
  margin-bottom: 10px;
}
.zq-entry__text {
  font-size: 14px;
  color: #718096;
  margin-bottom: 50px;
}
</style>
