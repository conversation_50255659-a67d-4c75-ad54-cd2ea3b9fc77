<template>
  <main class="order-result">
    <section class="order-result__content">
      <div class="order-result__icon-wrapper">
        <img
          class="order-result__icon"
          src="./assets/success.png"
          alt="提交成功"
          loading="eager"
          decoding="sync"
          width="130"
          height="130"
        />
      </div>

      <h1 class="order-result__title">{{ resultTitle }}</h1>

      <div class="order-result__description">
        <p class="order-result__desc-text">{{ descriptionText }}</p>
      </div>

      <WoButton
        type="secondary"
        size="large"
        block
        @click="handleGoToQuery"
      >
        {{ actionButtonText }}
      </WoButton>
    </section>
  </main>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { debounce } from 'lodash-es'
import { toRefs } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

// Props定义
const props = defineProps({
  resultTitle: {
    type: String,
    default: '提交成功'
  },
  descriptionText: {
    type: String,
    default: '5天内服务商确认发货情况'
  },
  actionButtonText: {
    type: String,
    default: '查看订单'
  },
  orderType: {
    type: Number,
    default: 3
  },
  redirectPath: {
    type: String,
    default: '/user/order/list'
  }
})

// Props解构
const {
  resultTitle,
  descriptionText,
  actionButtonText,
  orderType,
  redirectPath
} = toRefs(props)

const router = useRouter()

// 防抖处理的跳转函数，避免重复点击
const debouncedGoToQuery = debounce(() => {
  const timestamp = Date.now().toString()

  router.replace({
    path: redirectPath.value,
    query: {
      type: orderType.value,
      _t: timestamp
    }
  })
}, 300)

// 处理查看订单点击事件
const handleGoToQuery = () => {
  debouncedGoToQuery()
}
</script>

<style scoped lang="less">
.order-result {
  display: flex;
  justify-content: center;
  min-height: 100vh;
  background-color: #FFFFFF;
  padding: 30px 20px;
}

.order-result__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 500px;
}

.order-result__icon-wrapper {
  width: 100%;
  margin-bottom: 12px;
  container-type: inline-size;
  display: flex;
  justify-content: center;
  align-items: center;
}

.order-result__icon {
  width: 130px;
  height: 130px;
  object-fit: contain;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.order-result__title {
  font-size: 17px;
  color: #171E24;
  font-weight: 500;
  margin: 0 0 15px 0;
  text-align: center;
  font-display: swap;
}

.order-result__description {
  text-align: center;
  line-height: 1.5;
  padding: 0 30px;
  margin-bottom: 50px;
  width: 100%;
}

.order-result__desc-text {
  font-size: 15px;
  color: #4A5568;
  margin: 0;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.order-result__content > * {
  will-change: auto;
}

.order-result__icon {
  transform: translateZ(0);
}
</style>
