<!--
  政企商城企业列表组件
  功能：展示客户经理管理的企业列表，支持搜索和选择企业查看采购详情
  特性：实时搜索过滤、企业选择跳转、空状态处理、底部标签导航
  用途：客户经理查看和管理所属企业的入口页面
-->
<template>
  <!-- 企业列表容器 -->
  <div class="enterprise-list">
    <!-- 搜索区域 -->
    <div class="enterprise-list__search">
      <div class="search-wrapper">
        <input
          v-model="searchValue"
          type="text"
          placeholder="输入企业名称"
          class="search-input"
          @keyup.enter="handleSearch"
        />
        <button class="search-btn" @click="handleSearch">搜索</button>
      </div>
    </div>

    <!-- 企业列表内容区域 -->
    <div class="enterprise-list__content">
      <!-- 企业项列表 -->
      <div
        v-for="item in filteredEnterpriseList"
        :key="item.ciCode"
        class="enterprise-item"
        @click="handleEnterpriseSelect(item)"
      >
        <h3 class="enterprise-item__name">{{ item.ciName }}</h3>
        <div class="enterprise-item__action">
          <span>采购详情</span>
          <van-icon name="arrow" />
        </div>
      </div>

      <!-- 空状态展示 -->
      <div v-if="filteredEnterpriseList.length === 0" class="enterprise-list__empty">
        <van-empty :description="searchValue ? '没有找到企业' : '暂无企业数据'" />
      </div>
    </div>

    <!-- 底部标签导航 -->
    <TabComponent currentTab="enterprise" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 组件导入
import TabComponent from './components/Tab.vue'

// 工具函数导入
import { queryZqInfo } from '@/utils/zqInfo'

// 路由管理
const router = useRouter()

// 搜索状态管理
const searchValue = ref('')

// 企业列表数据管理
const enterpriseList = ref([])

// 过滤后的企业列表：根据搜索关键词实时过滤
const filteredEnterpriseList = computed(() => {
  if (!searchValue.value) return enterpriseList.value
  return enterpriseList.value.filter(item =>
    item.ciName.includes(searchValue.value)
  )
})

// 获取企业数据：从政企信息中获取客户经理管理的企业列表
const getEnterpriseData = () => {
  const customerManagerInfo = queryZqInfo()
  enterpriseList.value = customerManagerInfo.ciList || []
}

// 搜索处理：搜索逻辑由计算属性自动处理，此处预留扩展
const handleSearch = () => {
  // 搜索逻辑由 computed 属性 filteredEnterpriseList 处理
}

// 企业选择处理：跳转到对应企业的采购列表页面
const handleEnterpriseSelect = (enterprise) => {
  router.push({
    path: '/zq/manager/procurement-list',
    query: { ciCode: enterprise.ciCode }
  })
}

// 组件挂载时初始化数据
onMounted(() => {
  getEnterpriseData()
})
</script>

<style lang="less" scoped>
.enterprise-list {
  height: 100vh;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;

  &__search {
    position: sticky;
    top: 0;
    z-index: 10;
    padding: 10px;

    .search-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      border: 1px solid var(--wo-biz-theme-color);
      border-radius: 9999px;
      padding: 1px;

      .search-input {
        flex: 1;
        height: 28px;
        padding-left: 30px;
        background: url('./assets/search.png') no-repeat 9px center;
        background-size: 16px;
        font-size: 14px;
        color: #171E24;
        border: none;
        outline: none;

        &::placeholder {
          color: #718096;
        }
      }

      .search-btn {
        position: absolute;
        right: 1px;
        width: 60px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        color: #FFFFFF;
        font-size: 14px;
        font-weight: 400;
        background: var(--wo-biz-theme-gradient-2);
        border: none;
        border-radius: 50px;
        cursor: pointer;
      }
    }
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    padding-bottom: 66px;
    -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };

    .enterprise-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding: 0 15px;
      height: 76px;
      background-color: #FFFFFF;
      border-radius: 6px;
      box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.05);
      cursor: pointer;

      &__name {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
        padding-right: 15px;
        font-size: 15px;
        color: #171E24;
        font-weight: 500;
        margin: 0;
      }

      &__action {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        color: #4A5568;
        font-size: 14px;

        .van-icon {
          margin-left: 4px;
        }
      }
    }
  }

  &__empty {
    padding: 40px 0;
  }
}
</style>
