<template>
  <nav class="tab-nav">
    <van-tabbar fixed v-model:active="activeTab" @change="handleTabChange">
      <van-tabbar-item v-for="tab in tabList" :key="tab.name" :name="tab.name">
        <template #icon>
          <div class="tab-nav__icon-wrapper">
            <img :src="getTabIcon(tab)" :alt="tab.label" class="tab-nav__icon" />
          </div>
        </template>
        <span class="tab-nav__text" :class="{ 'tab-nav__text--active': activeTab === tab.name }">{{ tab.label }}</span>
      </van-tabbar-item>
    </van-tabbar>
  </nav>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// 导入图片资源
import tabEnterprise1 from '../assets/tab-enterprise1.png'
import tabEnterprise2 from '../assets/tab-enterprise2.png'
import tabGoods1 from '../assets/tab-goods1.png'
import tabGoods2 from '../assets/tab-goods2.png'

const TAB_CONFIG = {
  enterprise: {
    name: 'enterprise',
    label: '企业采购信息',
    route: '/zq/manager/enterprise-list',
    activeIcon: tabEnterprise2,
    inactiveIcon: tabEnterprise1
  },
  goods: {
    name: 'goods',
    label: '本省商品',
    route: '/zq/manager/goods-list',
    activeIcon: tabGoods2,
    inactiveIcon: tabGoods1
  }
}

const props = defineProps({
  currentTab: {
    type: String,
    default: 'enterprise',
    validator: (value) => ['enterprise', 'goods'].includes(value)
  }
})

const emit = defineEmits(['update:currentTab', 'tab-change'])

const router = useRouter()
const route = useRoute()

// 使用内部状态来管理当前激活的tab
const activeTab = ref(props.currentTab)

// 监听props变化
watch(() => props.currentTab, (newTab) => {
  activeTab.value = newTab
}, { immediate: true })

// 监听路由变化，自动更新激活状态
watch(() => route.path, (newPath) => {
  const matchedTab = Object.values(TAB_CONFIG).find(tab => tab.route === newPath)
  if (matchedTab && matchedTab.name !== activeTab.value) {
    activeTab.value = matchedTab.name
  }
}, { immediate: true })

const tabList = computed(() => Object.values(TAB_CONFIG))

const getTabIcon = (tab) => {
  return activeTab.value === tab.name ? tab.activeIcon : tab.inactiveIcon
}

const handleTabChange = (tabName) => {
  if (tabName === activeTab.value) return

  activeTab.value = tabName

  // 发射事件给父组件
  emit('update:currentTab', tabName)
  emit('tab-change', tabName)

  const tabConfig = TAB_CONFIG[tabName]
  if (tabConfig?.route && route.path !== tabConfig.route) {
    router.push(tabConfig.route)
  }
}
</script>

<style lang="less" scoped>
.tab-nav {
  width: 100%;

  &__icon-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__icon {
    width: 22px;
    height: 22px;
    object-fit: contain;
  }

  &__text {
    margin: 4px 0 8px;
    font-size: 11px;
    color: #718096;
    transition: color 0.2s ease;

    &--active {
      color: var(--wo-biz-theme-color);
      font-weight: 500;
    }
  }
}
</style>
