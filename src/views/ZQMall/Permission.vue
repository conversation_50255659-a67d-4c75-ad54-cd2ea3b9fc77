<!--
  政企商城权限控制组件
  功能：提供权限验证包装器，根据用户登录状态和角色权限控制内容显示
  特性：支持插槽内容、权限验证、无权限状态展示
  用法：包装需要权限控制的内容，自动处理权限验证逻辑
-->
<template>
  <!-- 权限控制容器 -->
  <div class="zq-permission">
    <!-- 无权限状态展示 -->
    <template v-if="permissionStatus < 0">
      <img
        src="@/assets/images/no-p.png"
        alt="无权限"
        class="zq-permission__icon"
        width="200"
        height="200"
        loading="eager"
        decoding="async"
        fetchpriority="high"
      >
      <p class="zq-permission__text">暂无该业务查看权限</p>
    </template>
    <!-- 有权限时显示插槽内容 -->
    <slot v-else />
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { queryZqInfo } from '@/utils/zqInfo'

// 权限状态：0-检查中，正数-有权限，负数-无权限
const permissionStatus = ref(0)

// 用户状态管理
const userStore = useUserStore()

// 组件挂载时进行权限验证
onMounted(async () => {
  // 首先验证用户登录状态
  const loginStatus = await userStore.login()

  if (loginStatus) {
    // 登录成功后检查政企权限
    const zqInfo = queryZqInfo() || {}
    const roleType = zqInfo.roleType

    // 角色类型为负数表示无权限，其他情况表示有权限
    permissionStatus.value = typeof roleType === 'number' && roleType < 0 ? roleType : 1
  } else {
    // 登录失败，设置为无权限状态
    permissionStatus.value = -99
  }
})
</script>

<style lang="less" scoped>
.zq-permission {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #FFFFFF;
}
.zq-permission__icon {
  width: 200px;
  height: 200px;
  margin-bottom: 10px;
}
.zq-permission__text {
  font-size: 14px;
  color: #718096;
  margin-bottom: 50px;
}
</style>
