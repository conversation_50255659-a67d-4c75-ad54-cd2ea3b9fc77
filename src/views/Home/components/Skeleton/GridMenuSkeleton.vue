<!--
  菜单网格骨架屏组件
  功能：在菜单网格数据加载时显示占位符动画
  特性：5列网格布局、流光动画效果、响应式设计
  用途：提升用户体验，避免菜单加载时的空白状态
-->
<template>
  <!-- 菜单网格骨架屏容器 -->
  <div class="grid-menu-skeleton">
    <!-- 骨架屏网格容器 -->
    <div class="skeleton-grid-container">
      <!-- 骨架屏菜单项 -->
      <div
        v-for="i in 5"
        :key="i"
        class="skeleton-grid-item"
      >
        <!-- 骨架屏图标 -->
        <div class="skeleton-icon"></div>
        <!-- 骨架屏标题 -->
        <div class="skeleton-title"></div>
        <!-- 骨架屏副标题 -->
        <div class="skeleton-subtitle"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 菜单网格骨架屏组件：无需额外逻辑，纯展示组件
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.grid-menu-skeleton {

  background: #ffffff;
  border-radius: 12px;
  margin: 8px 12px;

  padding: 5px;

  .skeleton-grid-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .skeleton-grid-item {
      width: calc(20% - 6.4px);
      display: flex;
      flex-direction: column;
      align-items: center;

      padding: 6px;
      text-align: center;
      box-sizing: border-box;

      .skeleton-icon {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

        width: 34px;
        height: 34px;
        border-radius: 4px;

        margin-bottom: 8px;
      }

      .skeleton-title {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 80%;

        height: 12px;
        margin-bottom: 2px;
      }

      .skeleton-subtitle {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 60%;

        height: 10px;
      }
    }
  }
}


@media (max-width: 375px) {
  .grid-menu-skeleton {
    .skeleton-grid-container {
      .skeleton-grid-item {

        padding: 6px;

        .skeleton-icon {

          width: 34px;
          height: 34px;
          margin-bottom: 8px;
        }

        .skeleton-title {
          height: 12px;
          margin-bottom: 2px;
        }

        .skeleton-subtitle {
          height: 10px;
        }
      }
    }
  }
}
</style>
