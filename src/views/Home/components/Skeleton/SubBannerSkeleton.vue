<template>
  <div class="sub-banner-skeleton">
    <div class="skeleton-sub-banner">
      <div class="skeleton-image"></div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.sub-banner-skeleton {

  position: relative;
  width: 100%;
  margin: 8px 12px 30px;

  .skeleton-sub-banner {
    position: relative;
    width: 100%;
    height: 120px;
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;

    .skeleton-image {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      width: 100%;
      height: 100%;
      border-radius: 12px;
      background-color: #f8f9fa;
    }
  }
}


@media (max-width: 375px) {
  .sub-banner-skeleton {
    margin: 6px 10px 24px;
    
    .skeleton-sub-banner {
      height: 100px;
    }
  }
}
</style>
