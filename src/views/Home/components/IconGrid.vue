<!--
  图标网格组件
  功能：展示图标菜单网格，支持网格和滚动两种显示模式
  特性：响应式布局、自定义列数、更多按钮、占位符填充、自定义滚动条
  模式：grid（网格模式）、scroll（横向滚动模式）
-->
<template>
  <!-- 图标网格容器 -->
  <div
    class="grid-menu"
    :class="[`columns-${dynamicColumns}`, { 'scroll-mode': displayMode === 'scroll' }]"
  >
    <!-- 网格内容容器 -->
    <div
      class="grid-container"
      ref="gridContainer"
      @scroll="handleScroll"
    >
      <!-- 图标项 -->
      <div
        v-for="(item, index) in displayMenuItems"
        :key="index"
        class="grid-item"
        :class="{
          'grid-item-more': item.isMore,
          'grid-item-placeholder': item.isPlaceholder
        }"
        @click="handleItemClick(item, index)"
      >
        <div class="item-content" v-if="!item.isPlaceholder">
          <!-- 图标区域 -->
          <div class="item-icon">
            <img
              v-if="item.icon"
              :src="item.icon"
              :alt="item.title"
            />
            <div v-else class="icon-placeholder">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" />
              </svg>
            </div>
          </div>

          <!-- 标题文本 -->
          <div class="item-title">{{ item.title }}</div>

          <!-- 副标题文本 -->
          <div v-if="item.subtitle" class="item-subtitle">{{ item.subtitle }}</div>

          <!-- 角标徽章 -->
          <div v-if="item.badge" class="item-badge">{{ item.badge }}</div>
        </div>
      </div>
    </div>

    <!-- 自定义滚动条（仅滚动模式） -->
    <div
      v-if="displayMode === 'scroll' && showScrollbar"
      class="custom-scrollbar"
    >
      <div
        class="scrollbar-thumb"
        :style="{ left: scrollbarLeft + 'px', width: scrollbarWidth + 'px' }"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs, ref, nextTick, onMounted, onUnmounted, watch } from 'vue'

// 组件属性定义
const props = defineProps({
  // 菜单项数据列表
  items: {
    type: Array,
    default: () => []
  },
  // 网格列数：支持2、4、5列布局
  columns: {
    type: Number,
    default: 5,
    validator: (value) => value === 2 || value === 4 || value === 5
  },
  // 是否显示更多按钮
  showMore: {
    type: Boolean,
    default: true
  },
  // 最大显示项目数量
  maxItems: {
    type: Number,
    default: 10
  },
  // 显示模式：grid（网格模式）、scroll（横向滚动模式）
  displayMode: {
    type: String,
    default: 'grid',
    validator: (value) => ['grid', 'scroll'].includes(value)
  }
})

// Props 解构：使用 toRefs 保持响应性
const { items, columns, showMore, maxItems, displayMode } = toRefs(props)

// 事件定义
const emit = defineEmits(['itemClick', 'moreClick'])

// 滚动相关状态管理
const gridContainer = ref(null)
const scrollbarLeft = ref(0)
const scrollbarWidth = ref(0)
const showScrollbar = ref(false)

// 菜单项处理：根据显示模式和配置处理菜单项数据
const processedMenuItems = computed(() => {
  let itemList = [...items.value]

  // 滚动模式：不限制数量，不添加更多按钮
  if (displayMode.value === 'scroll') {
    return itemList
  }

  // 网格模式：处理数量限制和更多按钮
  if (showMore.value && itemList.length > maxItems.value - 1) {
    // 超出限制时，截取并添加更多按钮
    itemList = itemList.slice(0, maxItems.value - 1)
    itemList.push({
      title: '更多',
      icon: null,
      isMore: true
    })
  } else if (itemList.length > maxItems.value) {
    // 超出限制但不显示更多按钮时，直接截取
    itemList = itemList.slice(0, maxItems.value)
  }

  return itemList
})

// 显示菜单项：包含占位符处理，确保网格布局整齐
const displayMenuItems = computed(() => {
  const items = processedMenuItems.value

  // 滚动模式：不需要占位符
  if (displayMode.value === 'scroll') {
    return items
  }

  // 网格模式：添加占位符确保行对齐
  const itemCount = items.length
  const cols = columns.value

  // 项目数量为0或正好是列数的倍数时，不需要占位符
  if (itemCount === 0 || itemCount % cols === 0) {
    return items
  }

  // 计算并添加占位符
  const placeholdersNeeded = cols - (itemCount % cols)
  const result = [...items]

  for (let i = 0; i < placeholdersNeeded; i++) {
    result.push({
      title: '',
      icon: null,
      isPlaceholder: true
    })
  }

  return result
})

// 动态列数计算：根据显示模式确定列数
const dynamicColumns = computed(() => {
  if (displayMode.value === 'scroll') {
    // 滚动模式：列数等于项目数量（单行显示）
    return processedMenuItems.value.length
  }

  // 网格模式：使用设定的列数
  return columns.value
})

// 项目宽度计算：根据显示模式确定每个项目的宽度
const itemWidth = computed(() => {
  if (displayMode.value === 'scroll') {
    // 滚动模式：固定宽度
    return '70px'
  }
  // 网格模式：平均分配宽度
  return `${100 / dynamicColumns.value}%`
})

// 处理滚动事件
const handleScroll = () => {
  if (displayMode.value !== 'scroll' || !gridContainer.value) return

  const container = gridContainer.value
  const scrollLeft = container.scrollLeft
  const scrollWidth = container.scrollWidth
  const clientWidth = container.clientWidth

  // 计算滚动条位置和宽度
  const scrollbarTrackWidth = 60 // 滚动条轨道宽度
  const scrollRatio = scrollLeft / (scrollWidth - clientWidth)
  const thumbWidth = Math.max(12, (clientWidth / scrollWidth) * scrollbarTrackWidth)
  const thumbLeft = scrollRatio * (scrollbarTrackWidth - thumbWidth)

  scrollbarLeft.value = thumbLeft
  scrollbarWidth.value = thumbWidth
}

// 更新滚动条显示状态
const updateScrollbarVisibility = () => {
  if (displayMode.value !== 'scroll' || !gridContainer.value) {
    showScrollbar.value = false
    return
  }

  const container = gridContainer.value
  showScrollbar.value = container.scrollWidth > container.clientWidth
}

// 处理项目点击
const handleItemClick = (item, index) => {
  // 占位符不响应点击
  if (item.isPlaceholder) {
    return
  }

  if (item.isMore) {
    emit('moreClick')
  } else {
    emit('itemClick', { item, index })
  }
}

// 监听数据变化
watch([displayMenuItems, displayMode], () => {
  nextTick(() => {
    updateScrollbarVisibility()
    handleScroll()
  })
}, { immediate: true })

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    updateScrollbarVisibility()
    handleScroll()
  })

  if (gridContainer.value) {
    gridContainer.value.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  if (gridContainer.value) {
    gridContainer.value.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style scoped lang="less">
.grid-menu {
  //padding: @padding-page;
  //background: #FFFFFF;
  position: relative;

  .grid-container {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin: 0;
    padding: 0;
  }

  // 滚动模式样式
  &.scroll-mode {
    padding-bottom: 20px; // 为滚动条留出空间

    .grid-container {
      flex-wrap: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      gap: 5px;
      //padding-bottom: 5px;

      // 隐藏原生滚动条
      &::-webkit-scrollbar {
        display: none;
      }
      -ms-overflow-style: none;
      scrollbar-width: none;
    }

    .grid-item {
      flex-shrink: 0;
      width: 70px;
    }
  }

  // 自定义滚动条
  .custom-scrollbar {
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: rgba(255, 122, 10, 0.2);
    border-radius: 2px;

    .scrollbar-thumb {
      position: absolute;
      top: 0;
      height: 100%;
      background-color: var(--wo-biz-theme-color);
      border-radius: 2px;
      transition: all 0.2s ease;
      min-width: 12px;
    }
  }

  .grid-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: v-bind(itemWidth);
    box-sizing: border-box;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 5px 0;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
      border-radius: 8px;
      transform: translateY(-1px);

      .item-icon {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
      }
    }

    &:active {
      transform: translateY(0);
      background-color: rgba(0, 0, 0, 0.04);
      border-radius: 8px;

      .item-icon {
        transform: scale(0.95);
      }
    }

    .item-content {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
    }

    .item-icon {
      width: 45px;
      height: 45px;
      margin-bottom: 3px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      img {
        width: 34px;
        height: 34px;
        object-fit: contain;
      }

      .icon-placeholder {
        color: #718096;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .item-title {
      font-size: 12px;
      font-weight: 500;
      color: #171E24;
      text-align: center;
      line-height: 1.2;
      max-width: 100%;
      overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
    }

    .item-subtitle {
      font-size: 11px;
      color: #718096;
      text-align: center;
      margin-top: 2px;
      line-height: 1.2;
      max-width: 100%;
      overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
    }

    .item-badge {
      position: absolute;
      top: -2px;
      right: -2px;
      background: var(--wo-biz-theme-gradient-3);
      color: #FFFFFF;
      font-size: 11px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 8px;
      min-width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 1px 3px rgba(255, 107, 107, 0.3);
      transform: scale(0.9);
    }

    &.grid-item-more {
      .item-icon {
        background: linear-gradient(135deg, #F8F9FA 0%, darken(#F8F9FA, 5%) 100%);
        border: 2px dashed #E2E8EE;
        box-shadow: none;

        &::after {
          content: '';
          width: 20px;
          height: 20px;
          background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 24' fill='none' stroke='%23666' stroke-width='2'%3E%3Cpath d='M12 5v14M5 12h14'/%3E%3C/svg%3E") no-repeat center;
          background-size: contain;
        }
      }

      .item-title {
        color: #4A5568;
      }

      &:hover .item-icon {
        background: linear-gradient(135deg, darken(#F8F9FA, 5%) 0%, darken(#F8F9FA, 10%) 100%);
        border-color: darken(#E2E8EE, 10%);
      }
    }
  }
}

.grid-menu {
  &.columns-2 {
    .grid-item {
      flex-direction: row;
      text-align: left;
      padding: 10px 0;

      .item-content {
        flex-direction: row;
        align-items: center;
        width: 100%;
        padding: 0 10px;
      }

      .item-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 0;
        margin-right: 12px;
        flex-shrink: 0;

        img {
          width: 36px;
          height: 36px;
        }
      }

      .item-title {
        font-size: 14px;
        text-align: left;
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
      }

      .item-subtitle {
        text-align: left;
        margin-top: 4px;
      }
    }
  }

  // 占位符样式
  .grid-item-placeholder {
    pointer-events: none;
    visibility: hidden;
  }
}
</style>
