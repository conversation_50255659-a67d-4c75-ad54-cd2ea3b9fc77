<!--
  司法助农业务首页组件
  功能：展示司法助农商城首页内容，包括轮播图和商品分类展示
  特性：支持轮播图展示、多级分类展示、分批加载、骨架屏展示、响应式布局
  业务：专为司法助农业务提供的分类导航和商品展示平台
-->
<template>
  <!-- 司法助农首页容器 -->
  <div class="sfzn-home">
    <!-- 轮播图区域 -->
    <section class="banner-section">
      <!-- 轮播图骨架屏 -->
      <BannerSkeleton v-if="skeletonStates.banner" />

      <!-- 轮播图内容 -->
      <div
        v-else-if="bannerImageList && bannerImageList.length > 0"
        class="banner-wrapper"
      >
        <GoodsSwiper
          :image-list="bannerImageList"
          :autoplay="true"
          :autoplay-delay="3000"
          :loop="true"
          :show-pagination="true"
          mode="landscape"
          @image-click="handleBannerClick"
        />
      </div>
    </section>

    <!-- 商品分类区域 -->
    <section class="category-section">
      <!-- 分类骨架屏 -->
      <CategorySkeleton v-if="skeletonStates.category" />

      <!-- 空状态展示 -->
      <div v-else-if="isCategoryEmpty" class="empty-state">
        <div class="empty-text">暂无分类数据</div>
      </div>

      <!-- 分类列表 -->
      <div v-else class="category-list">
        <!-- 分类项 -->
        <article
          v-for="item in categoryList"
          :key="item.id"
          class="category-item"
          :style="getCategoryItemStyle(item.img)"
        >
          <!-- 分类头部 -->
          <header class="category-header">
            <h2 class="category-title">{{ item.name }}</h2>
          </header>

          <!-- 分类内容 -->
          <div class="category-content">
            <!-- 子分类网格 -->
            <div class="subcategory-grid">
              <!-- 子分类项 -->
              <div
                v-for="subcategory in item.list"
                :key="subcategory.id"
                class="subcategory-item"
                @click="handleSubcategoryClick(subcategory)"
              >
                <!-- 子分类图片 -->
                <div class="subcategory-image-wrapper">
                  <img
                    class="subcategory-image"
                    :src="subcategory.img"
                    :alt="subcategory.name"
                    loading="lazy"
                    @error="handleImageError"
                  />
                </div>
                <!-- 子分类名称 -->
                <span class="subcategory-name">{{ subcategory.name }}</span>
              </div>
            </div>
          </div>
        </article>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { debounce } from 'lodash-es'
import { showToast } from 'vant'

// 组件导入
import GoodsSwiper from '@/components/Common/GoodsSwiper.vue'
import BannerSkeleton from '@/views/Home/components/Skeleton/BannerSkeleton.vue'
import CategorySkeleton from '../components/Skeleton/CategorySkeleton.vue'

// API 和工具函数导入
import { getBannerInfo } from '@/api/interface/bannerIcon'
import { getClassification } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { categoryPid } from '@utils/storage.js'

// 骨架屏状态管理
const skeletonStates = ref({
  banner: true,
  category: true
})

// 轮播图数据管理
const bannerImageList = ref([])

// 分类数据管理
const categoryList = ref([])
const categoryLoading = ref(false)

// 分类空状态判断：当不在加载中且分类列表为空时显示空状态
const isCategoryEmpty = computed(() => !categoryLoading.value && categoryList.value.length === 0)

// 路由管理
const route = useRoute()
const router = useRouter()

const getCategoryItemStyle = (imgUrl) => {
  if (!imgUrl) return {}
  return {
    backgroundImage: `url(${imgUrl})`,
    backgroundRepeat: 'no-repeat',
    backgroundSize: '100% 100%',
    backgroundPosition: 'center'
  }
}

const handleImageError = (event) => {
  console.warn('图片加载失败:', event.target.src)
  event.target.style.display = 'none'
}

const initSwiper = async () => {
  try {
    const params = {
      showPage: '1',
      bizCode: getBizCode('GOODS')
    }

    const [err, json] = await getBannerInfo(params)

    if (err) {
      console.error('获取轮播数据失败:', err.msg)
      showToast(err.msg)
      return
    }

    if (json && json.length > 0) {
      imageList.value = json.map((item, index) => ({
        id: item.id || index,
        url: item.imgUrl,
        alt: item.bannerChName || `轮播图 ${index + 1}`,
        title: item.bannerChName,
        linkUrl: item.url
      }))
    }
  } catch (error) {
    console.error('轮播初始化失败:', error)
    showToast('轮播初始化失败')
  } finally {
    await nextTick()
    setTimeout(() => {
      skeletonStates.value.banner = false
    }, 300)
  }
}

const onImageClick = debounce(({ item }) => {
  if (item.linkUrl) {
    window.location.href = item.linkUrl
  }
}, 300)

const fetchClassification = async (id) => {
  try {
    const [err, json] = await getClassification({
      bizCode: getBizCode('GOODS'),
      category_pid: id,
      page_no: 1,
      page_size: 500
    })

    if (err) {
      console.error('获取分类数据失败:', err.msg)
      return []
    }

    return json || []
  } catch (error) {
    console.error('获取分类数据失败:', error)
    return []
  }
}

const processCategoriesInBatches = async (categories, batchSize = 3) => {
  const results = []

  for (let i = 0; i < categories.length; i += batchSize) {
    const batch = categories.slice(i, i + batchSize)
    const batchPromises = batch.map(async (item, batchIndex) => {
      const actualIndex = i + batchIndex
      try {
        const thirdList = await fetchClassification(item.id)
        const sortedThirdList = thirdList.sort((a, b) => b.pos - a.pos)
        return { index: actualIndex, list: sortedThirdList }
      } catch (error) {
        console.error(`获取三级分类失败 (ID: ${item.id}):`, error)
        return { index: actualIndex, list: [] }
      }
    })

    const batchResults = await Promise.allSettled(batchPromises)
    results.push(...batchResults)
  }

  return results
}

const initCategoryData = async () => {
  if (loading.value) return

  try {
    loading.value = true

    const secondCategories = await fetchClassification(categoryPid.get())

    if (!secondCategories || secondCategories.length === 0) {
      secondList.value = []
      return
    }

    const sortedSecondCategories = secondCategories.sort((a, b) => b.pos - a.pos)

    const transList = sortedSecondCategories.map(item => ({
      id: item.id,
      name: item.name,
      img: item.img,
      list: []
    }))

    secondList.value = transList

    const batchResults = await processCategoriesInBatches(sortedSecondCategories)

    batchResults.forEach(result => {
      if (result.status === 'fulfilled' && result.value) {
        const { index, list } = result.value
        if (secondList.value[index]) {
          secondList.value[index].list = list
        }
      }
    })

  } catch (error) {
    console.error('初始化分类数据失败:', error)
    showToast('初始化分类数据失败')
    secondList.value = []
  } finally {
    loading.value = false
    await nextTick()
    setTimeout(() => {
      skeletonStates.value.category = false
    }, 300)
  }
}

const onSubcategoryClick = debounce((item) => {
  const timestamp = Date.now()
  router.push({
    path: `/goodslist/${item.id}`,
    query: {
      ...route.query,
      timestamp
    }
  })
}, 300)

const initializeApp = async () => {
  try {
    if (route.query.category_pid) {
      categoryPid.set(route.query.category_pid)
    }

    await Promise.allSettled([
      initSwiper(),
      initCategoryData()
    ])
  } catch (error) {
    console.error('应用初始化失败:', error)
    showToast('页面初始化失败')
  }
}

onMounted(async () => {
  await initializeApp()
})
</script>

<style lang='less' scoped>
.sfzn-home {
  background-color: #F8F9FA;
  height: 100%;
  overflow: auto;
}

.banner-section {
  padding: 4px 5px 0;

  .banner-wrapper {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.category-section {
  padding: 8px 5px;

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    background-color: #FFFFFF;
    border-radius: 12px;
    margin-top: 8px;

    .empty-text {
      font-size: 16px;
      color: #718096;
      font-weight: 400;
    }
  }
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-item {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  }
}

.category-header {
  padding: 10px;
  //background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  //backdrop-filter: blur(10px);

  .category-title {
    font-size: 18px;
    color: #171E24;
    font-weight: 500;
    line-height: 1.2;
    margin: 0;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }
}

.category-content {
  padding: 10px 5px;
}

.subcategory-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.subcategory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  width: calc(33.333% - 14px);
  min-width: 80px;

  &:hover {
    background-color: #fff7f0;
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
    background-color: darken(#fff7f0, 5%);
  }
}

.subcategory-image-wrapper {
  width: 68px;
  height: 68px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 5px;
  background-color: #F8F9FA;
  display: flex;
  align-items: center;
  justify-content: center;

  .subcategory-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.subcategory-name {
  font-size: 14px;
  color: #171E24;
  font-weight: 400;
  line-height: 1.2;
  display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
  max-width: 100%;
  word-break: break-all;
}
</style>
