<!--
  劳保商城首页组件
  功能：展示劳保商城首页内容，包括轮播图、菜单网格、商品瀑布流
  特性：支持商品池切换、瀑布流加载、骨架屏展示
  业务：劳保商城提供的商品购买平台
-->
<template>
  <!-- 劳动者首页布局容器 -->
  <BaseHomeLayout
    home-class="labor-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <template #main-content>
      <!-- 商品瀑布流区域 -->
      <WaterfallSection
        :waterfall-goods-list="waterfallGoodsList"
        :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished"
        :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete"
        :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        @goods-click="handleGoodsClick"
        @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender"
      />
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 组件导入
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'

// 组合式函数导入
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'

// 首页数据管理：获取轮播图、菜单、商品列表等数据和状态
const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  waterfallGoodsList,
  waterfallLoading,
  waterfallFinished,
  waterfallButtonCanShow,
  waterfallRenderComplete,
  getHeaderBannerList,
  getIconList,
  getWaterfallList,
  resetWaterfallState,
  getPartionListData
} = useHomeData()

// 首页导航管理：处理各种点击事件和页面跳转
const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch
} = useHomeNavigation()

// 商品分区数据管理
const typeList = ref([])
const goodsPoolIdSelected = ref('')

// 瀑布流渲染完成处理：标记渲染状态完成
const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

// 瀑布流加载更多处理：加载下一页商品数据
const handleWaterfallLoadMore = () => {
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}

// 商品池切换处理：重置状态并加载新的商品数据
const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  resetWaterfallState()
  getWaterfallList(id, sortType, false)
}

// 页面数据初始化：获取商品分区列表并加载默认商品池
const initPageData = async () => {
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  if (typeList.value.length > 0) {
    const defaultPartition = typeList.value[0]
    goodsPoolIdSelected.value = defaultPartition.id
    changeGoodsPool(defaultPartition.id)
  }
}

// 组件挂载时初始化：并行加载基础数据和页面数据
onMounted(() => {
  getHeaderBannerList()
  getIconList(6) // 劳动者首页使用 showPage: 6
  initPageData()
})

// 组件卸载时清理：预留清理逻辑
onUnmounted(() => {
  // 清理工作预留
})
</script>

<style scoped lang="less">
.labor-home {
  // 首页样式预留
}
</style>
