<template>
  <div class="goods-list-page">
    <header ref="pageHeaderRef" class="page-header">
      <ProvinceFilter @confirm="handleProvinceFilterConfirm" />

      <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirectToSearch="bizCode !== 'zq'"
        :redirectUrl="'/search'" @search="handleSearch" @clickable="handleSearchClick">
        <template #right-action>
          <button v-if="bizCode !== 'zq'" class="layout-toggle" @click="toggleLayout" type="button">
            <img :src="isWaterfallLayout ? switchLayout2Img : switchLayoutImg" alt="切换布局" width="20" height="20" />
          </button>
        </template>
      </SearchHeader>

      <SortFilterBar v-if="bizCode !== 'zq'" :sort-type="sortType" :sort-order="sortOrder"
        :has-filter-conditions="hasFilterConditions" @sort-change="handleSortChange" @filter-toggle="toggleFilter" />
    </header>

    <main class="goods-content">
      <GoodsListLayout :goods-list="goodsList" :is-loading="isLoading" :loading="loading" :finished="finished"
        :is-waterfall="isWaterfallLayout" :breakpoints="breakpoints" empty-description="本地区无货" @load-more="onLoad"
        @item-click="goToDetail" @add-cart="addOneCart" @update:loading="(val) => loading = val" />
    </main>

    <FloatingBubble :offset="floatingBubbleOffset" @go-to-cart="goToCart"
      :is-show-cart="bizCode !== 'zq' || (bizCode === 'zq' && roleType !== '2')" />

    <FilterPopup v-if="bizCode !== 'zq'" v-model:show="isPopupShow" v-model="filterCriteria"
      :location-text="locationText" :category-id="categoryId" @switch-address="setSwitchAddressPopupShow"
      @confirm="handleFilterConfirm" @reset="handleFilterReset" />

    <AddressSwitchPopup v-if="bizCode !== 'zq'" v-model:show="isSwitchAddressPopupShow"
      @address-changed="handleAddressChanged" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick, watch, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { filter, get } from 'lodash-es'
import SearchHeader from '@components/Common/SearchHeader.vue'
import AddressSwitchPopup from '@components/Common/FilterTools/AddressSwitchPopup.vue'
import FilterPopup from '@components/Common/FilterTools/FilterPopup.vue'
import FloatingBubble from '@components/Common/FloatingBubble.vue'
import SortFilterBar from '@components/Common/FilterTools/SortFilterBar.vue'
import GoodsListLayout from '@components/GoodsListCommon/GoodsListLayout.vue'
import { useGoodsList } from '@/composables/useGoodsList.js'
import { getBizCode } from '@utils/curEnv.js'
import { skuPageList, zqQuerySimplified } from '@api/index.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import switchLayoutImg from '@/static/images/switch-layout.png'
import switchLayout2Img from '@/static/images/switch-layout2.png'
import { useUserStore } from '@store/modules/user.js'
import { useProvinceServiceStore } from '@store/modules/provinceService.js'
import { getDefaultBreakpoints } from '@/config/responsive.js'
import { getCustomerManagerInfo, getEnterpriseManagerInfo, queryZqInfo } from '@utils/zqInfo.js'
// 异步加载 ProvinceFilter 组件
const ProvinceFilter = defineAsyncComponent(() => import('@components/ZQCommon/ProvinceFilter.vue'))

const userStore = useUserStore()
const provinceServiceStore = useProvinceServiceStore()
const route = useRoute()
const router = useRouter()

const bizCode = getBizCode()

const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})

// 使用商品列表组合函数
const {
  goodsList,
  loading,
  finished,
  isLoading,
  pageNo,
  pageSize,
  filterCriteria,
  hasFilterConditions,
  locationText,
  addressInfo,
  resetList,
  processGoodsData,
  applyStockFilter,
  goToCart,
  addOneCart,
  handleFilterReset,
  handleAddressChanged
} = useGoodsList()

// 页面特有状态
const floatingBubbleOffset = ref({ bottom: 150 })
const searchKeyword = ref('')
const sortType = ref('sort')
const sortOrder = ref('desc')
const isWaterfallLayout = ref(false)
const categoryId = ref('')
const isPopupShow = ref(false)
const isSwitchAddressPopupShow = ref(false)

// 动态计算头部高度
const headerHeight = ref(0)
const pageHeaderRef = ref(null)

const dynamicPaddingTop = computed(() => {
  return headerHeight.value >= 0 ? `${headerHeight.value}px` : '85px'
})


// 瀑布流配置
const breakpoints = ref(getDefaultBreakpoints())

// 页面方法
const toggleLayout = () => {
  isWaterfallLayout.value = !isWaterfallLayout.value
}

const goToDetail = (item) => {
  const firstSku = get(item, 'skuList[0]', {})
  const { goodsId, skuId } = firstSku

  if (goodsId && skuId) {
    router.push(`/goodsdetail/${goodsId}/${skuId}`)
  }
}

const toggleFilter = () => {
  isPopupShow.value = !isPopupShow.value
}

const setSwitchAddressPopupShow = () => {
  isSwitchAddressPopupShow.value = true
}

const handleSearch = (keyWord) => {
  if (!keyWord) {
    showToast('请输入搜索内容')
    return
  }
  const timestamp = Date.parse(new Date())
  const testDMX = get(route.query, 'testDMX', false)

  // 根据roleType决定参数来源
  const zqInfo = queryZqInfo()
  let supplierCode = ''
  let proStr = ''

  if (roleType.value === '4') {
    // 当roleType为'4'时，从provinceService store获取数据
    supplierCode = provinceServiceStore.selectedIsvId || ''
    proStr = provinceServiceStore.selectedAreaId || ''
  } else {
    // 其他角色从zqInfo获取数据
    supplierCode = zqInfo.isvList[0]?.isvId || ''
    proStr = zqInfo.provinceCode.join(',')
  }

  router.push('/search/list?timestamp=' + timestamp + '&keyword=' + keyWord + '&testDMX=' + testDMX + '&supplierCode=' + supplierCode + '&proStr=' + proStr)
  searchKeyword.value = ''
}

const handleSearchClick = () => {
  console.log('搜索框被点击，即将跳转到搜索页面')
}

const handleSortChange = ({ type, currentSortType, currentSortOrder }) => {
  if (currentSortType === type) {
    if (type === 'price' || type === 'sale') {
      sortOrder.value = currentSortOrder === 'asc' ? 'desc' : 'asc'
    }
  } else {
    sortType.value = type
    sortOrder.value = ''
  }

  reloadGoodsList()
}

const handleFilterConfirm = () => {
  reloadGoodsList()
}

// 处理省分筛选确认
const handleProvinceFilterConfirm = (selection) => {
  console.log('省分筛选选择:', selection)
  // 重置列表并重新加载数据
  reloadGoodsList()
}

// 重新加载商品列表的通用方法
const reloadGoodsList = () => {
  resetList()
  fetchGoodsList()
}

// 获取商品列表
const fetchGoodsList = async () => {
  if (pageNo.value === 1) {
    isLoading.value = true
  }

  const brandList = filter(filterCriteria.value.brandsList, 'isSelected').map(item => item.value)

  const zqInfo = queryZqInfo()

  // 根据roleType决定参数来源
  let supplierCode = ''
  let proStr = ''

  if(bizCode === 'zq') {
    if (roleType.value === '4') {
      // 当roleType为'4'时，从provinceService store获取数据
      supplierCode = provinceServiceStore.selectedIsvId || ''
      proStr = provinceServiceStore.selectedAreaId || ''
    } else {
      // 其他角色从zqInfo获取数据
      supplierCode = zqInfo.isvList[0]?.isvId || ''
      proStr = zqInfo.provinceCode.join(',')
    }
  }

  showLoadingToast()

  let apiResult
  if (bizCode === 'zq') {
    const [zqErr, zqJson] = await zqQuerySimplified({
      bizCode: getBizCode('GOODS'),
      type: 1,
      id: categoryId.value,
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      roleType: roleType.value,
      supplierCode: supplierCode,
      proStr: proStr,
    })
    apiResult = { err: zqErr, json: zqJson }
  } else {
    const [skuErr, skuJson] = await skuPageList({
      bizCode: getBizCode('GOODS'),
      categoryId: categoryId.value,
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      type: sortType.value,
      sort: sortOrder.value,
      brandList: JSON.stringify(brandList),
      priceFrom: filterCriteria.value.minPrice !== '' ? Number(filterCriteria.value.minPrice * 100) : '',
      priceTo: filterCriteria.value.maxPrice !== '' ? Number(filterCriteria.value.maxPrice * 100) : '',
      addressInfo: addressInfo.value
    })
    apiResult = { err: skuErr, json: skuJson }
  }

  const { err, json } = apiResult

  closeToast()

  if (pageNo.value === 1) {
    goodsList.value = []
  }

  loading.value = false
  isLoading.value = false

  if (!err) {
    // 政企用户使用 goodsList，非政企用户使用 skuList
    const dataList = bizCode === 'zq' ? json?.goodsList : json?.skuList

    if (json && dataList && dataList.length > 0) {
      const processedList = processGoodsData(dataList)
      const filteredList = applyStockFilter(processedList)
      if (filteredList.length <= 0 && json.cacheType === '1') {
        pageNo.value++
        fetchGoodsList()
        return
      }

      goodsList.value = goodsList.value.concat(filteredList)

      if (json.cacheType === '1') {
        pageNo.value++
      } else {
        finished.value = true
      }
    } else {
      if (!json || (json && json.cacheType === '0')) {
        finished.value = true
        return
      }
      pageNo.value++
      fetchGoodsList()
    }
  } else {
    console.error('获取商品列表失败:', err.msg)
  }
}

const onLoad = () => {
  fetchGoodsList()
}

// 计算头部高度的方法
const calculateHeaderHeight = () => {
  nextTick(() => {
    if (pageHeaderRef.value) {
      // 直接获取实际的 header 高度
      headerHeight.value = pageHeaderRef.value.offsetHeight
    } else {
      // 备用计算方式
      let totalHeight = 0

      // ProvinceFilter 组件高度（当显示时）
      if (roleType.value === '4') {
        totalHeight += 44 // ProvinceFilter 的大概高度
      }

      // SearchHeader 组件高度
      totalHeight += 44 // SearchHeader 的大概高度

      // 如果有 SortFilterBar 组件显示，则加上其高度
      if (bizCode !== 'zq') {
        totalHeight += 44 // SortFilterBar 的高度
      }

      headerHeight.value = totalHeight
    }
  })
}

// 监听bizCode和roleType变化，重新计算头部高度
watch(() => [bizCode, roleType.value], () => {
  calculateHeaderHeight()
}, { flush: 'post' })

onMounted(async () => {
  categoryId.value = route.params.id

  if (route.query.keyword) {
    searchKeyword.value = route.query.keyword
  }

  await userStore.queryDefaultAddr({ force: true })

  // 移除预先初始化省分服务数据，改为按需加载

  // 计算头部高度
  calculateHeaderHeight()

  fetchGoodsList()
})
</script>

<style scoped lang="less">
.goods-list-page {
  padding-top: v-bind(dynamicPaddingTop);

  .page-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    .layout-toggle {
      margin-left: 12px;
      padding: 4px;
      background: none;
      border: none;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .goods-content {
    padding: 0 10px;
  }
}
</style>
