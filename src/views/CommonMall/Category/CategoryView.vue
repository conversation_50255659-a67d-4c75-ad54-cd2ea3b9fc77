<template>
  <MainLayout scroll="auto">
    <ProvinceFilter/>
    <div class="category-page">
      <!-- 搜索头部 -->
      <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirectToSearch="bizCode !== 'zq'"
        :redirectUrl="'/search'" @search="handleSearch" />

      <!-- 分类主体布局 -->
      <div class="category-page__layout">
        <!-- 侧边栏 -->
        <CategorySidebar :categories="firstCategories" :is-loading="isFirstCategoryLoading"
          :active-index="activeFirstCategory" @change="handleFirstCategoryChange" ref="sidebarRef" />

        <!-- 主内容区 -->
        <main class="category-page__main" ref="categoryMainRef">
          <!-- 一级分类加载骨架屏 -->
          <CategorySkeleton v-if="isFirstCategoryLoading" type="content" :count="3" :items-per-section="3" />

          <!-- 二级分类加载骨架屏 -->
          <CategorySkeleton v-else-if="isSecondCategoryLoading" type="content" :count="3" :items-per-section="3" />

          <!-- 分类内容 -->
          <div v-else class="category-page__content">
            <section v-for="(group, index) in thirdCategoriesGroups" :key="group.id || index"
              class="category-page__section" :data-category-id="group.id">
              <h3 class="category-page__section-title">{{ group.title }}</h3>

              <!-- 分类项目列表 -->
              <div v-if="group.items.length > 0" class="category-page__items">
                <CategoryItem v-for="item in group.items" :key="item.id" :id="item.id" :name="item.name"
                  :image-url="item.img" :width-style="itemWidthStyle" :default-icon="defaultIcon"
                  @click="handleCategoryClick" />
              </div>

              <!-- 加载状态骨架屏 -->
              <CategorySkeleton v-else-if="group.isLoading" type="grid" :count="3" />

              <!-- 空状态 -->
              <div v-else-if="group.isEmpty" class="category-page__empty">
                <span class="category-page__empty-text">暂无商品分类</span>
              </div>

              <!-- 占位区域 -->
              <div v-else class="category-page__placeholder">
                <div class="category-page__load-trigger" :data-second-id="group.id"></div>
              </div>
            </section>
          </div>
        </main>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useElementSize } from '@vueuse/core'
import { debounce, get } from 'lodash-es'
import { closeToast, showLoadingToast, showToast } from 'vant'
import SearchHeader from '@components/Common/SearchHeader.vue'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import CategorySkeleton from './components/CategorySkeleton.vue'
import CategoryItem from './components/CategoryItem.vue'
import CategorySidebar from './components/CategorySidebar.vue'
import { getClassification } from '@api/interface/goods.js'
import { getBizCode } from '@utils/curEnv.js'
import { useUserStore } from '@store/modules/user.js'
import { useProvinceServiceStore } from '@store/modules/provinceService.js'
import { getCustomerManagerInfo, getEnterpriseManagerInfo, queryZqInfo } from '@utils/zqInfo.js'
import ProvinceFilter from '@components/ZQCommon/ProvinceFilter.vue'
const router = useRouter()
const route = useRoute()

const userStore = useUserStore()
const provinceServiceStore = useProvinceServiceStore()
const bizCode = getBizCode()

const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})

// 常量
const INITIAL_LOAD_COUNT = 3
const LOAD_THRESHOLD = 200
const defaultIcon = 'https://img01.yzcdn.cn/vant/cat.jpeg'

// 基础数据
const searchKeyword = ref('')

// 组件引用
const sidebarRef = ref(null)
const categoryMainRef = ref(null)

// 分类数据
const firstCategories = ref([])
const secondCategories = ref([])
const thirdCategories = ref(new Map())
const activeFirstCategory = ref(0)

// 加载状态
const isFirstCategoryLoading = ref(true)
const isSecondCategoryLoading = ref(false)
const loadingThirdCategories = ref(new Set())
const loadedSecondCategoryIds = ref(new Set())

// 滚动监听器
let scrollListener = null

// 响应式布局
const { width: containerWidth } = useElementSize(categoryMainRef)
const itemsPerRow = computed(() => Math.max(Math.floor(containerWidth.value / 100), 3))
const itemWidthStyle = computed(() => `${100 / itemsPerRow.value}%`)

// 计算三级分类分组显示数据
const thirdCategoriesGroups = computed(() => {
  const groups = []
  secondCategories.value.forEach(secondCategory => {
    const items = thirdCategories.value.get(secondCategory.id) || []
    const isLoading = loadingThirdCategories.value.has(secondCategory.id)
    const isLoaded = loadedSecondCategoryIds.value.has(secondCategory.id)

    groups.push({
      id: secondCategory.id,
      title: secondCategory.name,
      items: items,
      isLoading: isLoading,
      isLoaded: isLoaded,
      isEmpty: isLoaded && items.length === 0
    })
  })
  return groups
})

// 搜索处理
const handleSearch = (keyWord) => {
  if (!keyWord) {
    showToast('请输入搜索内容')
    return
  }
  const timestamp = Date.parse(new Date())
  const testDMX = get(route.query, 'testDMX', false)

  // 根据roleType决定参数来源
  const zqInfo = queryZqInfo()
  let supplierCode = roleType.value !== '4' ? (zqInfo.isvList[0]?.isvId || '') : ''
  let proStr = roleType.value !== '4' ? zqInfo.provinceCode.join(',') : ''

  if (roleType.value === '4') {
    // 当roleType为'4'时，从provinceService store获取数据
    supplierCode = provinceServiceStore.selectedIsvId || ''
    proStr = provinceServiceStore.selectedAreaId || ''
  }

  router.push('/search/list?timestamp=' + timestamp + '&keyword=' + keyWord + '&testDMX=' + testDMX + '&supplierCode=' + supplierCode + '&proStr=' + proStr)
  searchKeyword.value = ''
}

// 一级分类切换处理
const handleFirstCategoryChange = (index, category) => {
  if (!category) return

  // 滚动到顶部
  if (categoryMainRef.value) {
    categoryMainRef.value.scrollTop = 0
  }

  // 更新路由（使用正确的语法）
  router.replace(`/category/${category.id}`)
  // 获取对应的二级和三级分类
  fetchSecondAndThirdCategories(category.id)
}

// 分类项点击处理
const handleCategoryClick = (categoryData) => {
  router.push({ path: `/goodslist/${categoryData.id}` })
}

// 获取分类数据
const fetchCategories = async (id = '') => {
  try {
    // 设置加载状态
    if (id === '') {
      isFirstCategoryLoading.value = true
    } else if (!isFirstCategoryLoading.value) {
      isSecondCategoryLoading.value = true
      showLoadingToast()
    }

    // 调用API
    const [err, data] = await getClassification({
      bizCode: getBizCode('GOODS'),
      category_pid: id,
      page_no: 1,
      page_size: 500
    })

    if (err) {
      showToast('获取分类数据失败')
      return
    }

    if (data && Array.isArray(data)) {
      await processCategories(data, id)
    }
  } catch (error) {
    showToast('获取分类数据失败')
  } finally {
    // 关闭加载状态
    if (id === '') {
      isFirstCategoryLoading.value = false
    } else {
      isSecondCategoryLoading.value = false
    }
    closeToast()
  }
}

// 处理分类数据
const processCategories = async (data, id) => {
  if (id === '') {
    // 处理一级分类
    firstCategories.value = data.filter(item => item.depth === 1)

    // 根据路由参数设置当前分类
    const routeCategoryId = route.params.id

    if (routeCategoryId && firstCategories.value.length > 0) {
      // 查找路由参数对应的一级分类（处理类型转换）
      const categoryIndex = firstCategories.value.findIndex(category =>
        String(category.id) === String(routeCategoryId)
      )
      if (categoryIndex !== -1) {
        // 找到对应分类，设置为当前选中
        activeFirstCategory.value = categoryIndex
        await fetchSecondAndThirdCategories(firstCategories.value[categoryIndex].id)
        // 滚动到对应的一级分类位置
        nextTick(() => {
          setTimeout(() => {
            if (sidebarRef.value) {
              sidebarRef.value.scrollToCategory(categoryIndex)
            }
          }, 200)
        })
      } else {
        // 未找到对应分类，默认选择第一个
        activeFirstCategory.value = 0
        await fetchSecondAndThirdCategories(firstCategories.value[0].id)
      }
    } else if (firstCategories.value.length > 0) {
      // 没有路由参数，默认选择第一个分类
      activeFirstCategory.value = 0
      await fetchSecondAndThirdCategories(firstCategories.value[0].id)
    }
  } else {
    // 处理二级分类
    const secondLevel = data.filter(item => item.depth === 2)
    secondCategories.value = secondLevel

    // 清空之前的三级分类数据
    thirdCategories.value.clear()
    loadedSecondCategoryIds.value.clear()
    loadingThirdCategories.value.clear()

    // 加载初始的三级分类
    if (secondLevel.length > 0) {
      await loadInitialThirdCategories(secondLevel)
      nextTick(() => setupScrollListener())
    }
  }
}

// 获取二级和三级分类
const fetchSecondAndThirdCategories = async (firstCategoryId) => {
  if (!firstCategoryId) return
  await fetchCategories(firstCategoryId)
}

// 获取单个二级分类的三级分类
const fetchThirdCategories = async (secondCategoryId) => {
  if (!secondCategoryId) return []

  try {
    const [err, data] = await getClassification({
      bizCode: getBizCode('GOODS'),
      category_pid: secondCategoryId,
      page_no: 1,
      page_size: 500
    })

    if (err || !data) return []

    return data.filter(item => item.depth === 3)
  } catch (error) {
    return []
  }
}

// 加载初始的几个二级分类的三级分类
const loadInitialThirdCategories = async (secondLevel) => {
  const initialCategories = secondLevel.slice(0, INITIAL_LOAD_COUNT)

  const loadPromises = initialCategories.map(async (secondCategory) => {
    loadingThirdCategories.value.add(secondCategory.id)

    try {
      const thirdItems = await fetchThirdCategories(secondCategory.id)
      thirdCategories.value.set(secondCategory.id, thirdItems)
      loadedSecondCategoryIds.value.add(secondCategory.id)
    } catch (error) {
      thirdCategories.value.set(secondCategory.id, [])
      loadedSecondCategoryIds.value.add(secondCategory.id)
    } finally {
      loadingThirdCategories.value.delete(secondCategory.id)
    }
  })

  await Promise.all(loadPromises)
}

// 懒加载单个二级分类的三级分类
const loadThirdCategoryLazy = async (secondCategoryId) => {
  if (loadedSecondCategoryIds.value.has(secondCategoryId) ||
    loadingThirdCategories.value.has(secondCategoryId)) {
    return
  }

  loadingThirdCategories.value.add(secondCategoryId)

  try {
    const thirdItems = await fetchThirdCategories(secondCategoryId)
    thirdCategories.value.set(secondCategoryId, thirdItems)
    loadedSecondCategoryIds.value.add(secondCategoryId)
  } catch (error) {
    thirdCategories.value.set(secondCategoryId, [])
    loadedSecondCategoryIds.value.add(secondCategoryId)
  } finally {
    loadingThirdCategories.value.delete(secondCategoryId)
  }
}




// 设置滚动监听，实现懒加载
const setupScrollListener = () => {
  if (!categoryMainRef.value) return

  // 移除之前的监听器
  if (scrollListener) {
    categoryMainRef.value.removeEventListener('scroll', scrollListener)
  }

  // 防抖处理滚动事件
  const debouncedCheck = debounce(() => {
    checkAndLoadVisibleCategories()
  }, 100)

  scrollListener = debouncedCheck
  categoryMainRef.value.addEventListener('scroll', scrollListener, { passive: true })

  // 初始检查
  nextTick(() => checkAndLoadVisibleCategories())
}

// 检查并加载可见的分类
const checkAndLoadVisibleCategories = () => {
  if (!categoryMainRef.value) return

  const container = categoryMainRef.value
  const containerRect = container.getBoundingClientRect()
  const containerTop = containerRect.top
  const containerBottom = containerRect.bottom

  const sections = container.querySelectorAll('.category-page__section')

  sections.forEach(section => {
    const sectionRect = section.getBoundingClientRect()
    const secondCategoryId = section.dataset.categoryId

    if (!secondCategoryId || loadedSecondCategoryIds.value.has(secondCategoryId)) {
      return
    }

    // 检查是否在可视区域内或即将进入
    const isVisible = sectionRect.top < containerBottom + LOAD_THRESHOLD &&
      sectionRect.bottom > containerTop - LOAD_THRESHOLD

    if (isVisible) {
      loadThirdCategoryLazy(secondCategoryId)
    }
  })
}

// 组件挂载时获取分类数据
onMounted(async () => {
  await fetchCategories()
})

// 组件卸载时清理事件监听
onBeforeUnmount(() => {
  if (scrollListener && categoryMainRef.value) {
    categoryMainRef.value.removeEventListener('scroll', scrollListener)
  }
})
</script>

<style scoped lang="less">
.category-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #F8F9FA;

  &__layout {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  &__main {
    flex: 1;
    overflow-y: auto;
    background-color: #FFFFFF;
    contain: layout style paint;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };
  }

  &__content {
    padding: 10px;
  }

  &__section {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(227, 227, 242, 0.5);

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__section-title {
    margin: 0 0 10px 0;
    padding: 0;
    font-size: 14px;
    font-weight: 500;
    color: #171E24;
    line-height: 1.4;
  }

  &__items {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -5px;
  }

  &__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
  }

  &__empty-text {
    font-size: 13px;
    color: #718096;
  }

  &__placeholder {
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__load-trigger {
    width: 100%;
    height: 20px;
    background: transparent;
  }
}
</style>
