<template>
  <div class="address-page" :class="{ 'address-page--has-data': hasAddresses }">
    <div class="address-page__container">
      <div class="address-page__list" v-if="loading">
        <div v-for="index in skeletonCount" :key="`skeleton-${index}`" class="address-page__item">
          <div class="address-skeleton">
            <div class="address-skeleton__content">
              <div class="address-skeleton__header">
                <div class="address-skeleton__user-info">
                  <div class="skeleton-line skeleton-name"></div>
                  <div class="skeleton-line skeleton-phone"></div>
                </div>
              </div>
              <div class="skeleton-line skeleton-address"></div>
              <div class="skeleton-line skeleton-address-detail"></div>
            </div>
            <div class="address-skeleton__actions">
              <div class="skeleton-line skeleton-btn"></div>
              <div class="skeleton-line skeleton-btn"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="address-page__list" ref="addressListRef" v-if="!loading && hasAddresses">
        <div v-for="address in addressList" :key="address.addressId"
          :ref="el => setAddressItemRef(el, address.addressId)" :data-address-id="address.addressId"
          class="address-page__item">
          <AddressItem :address="address" @click="handleSelectAddress" @edit="handleEditAddress"
            @delete="handleDeleteAddress" />
        </div>
      </div>

      <WoEmpty v-if="!loading && !hasAddresses" @add="handleAddAddress" image="@/static/images/empty-address.png"
        description="暂无收货地址" class="address-page__empty" />
    </div>

    <AddressActionBar size="xlarge" @add="handleAddAddress" class="address-page__actions" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, shallowRef, readonly, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { debounce, isEmpty } from 'lodash-es'
import { useUserStore } from '@store/modules/user.js'
import { updateUserDefaultAddr, deleteAddr } from '@api/index.js'
import AddressItem from '@components/Common/Address/AddressItem.vue'
import WoEmpty from '@components/WoElementCom/WoEmpty.vue'
import AddressActionBar from '@components/WoElementCom/WoActionBar.vue'
import { useAlert } from '@/composables/index.js'

const router = useRouter()
const userStore = useUserStore()
const $alert = useAlert()

const addressList = shallowRef([])
const loading = ref(false)
const error = ref(null)
const addressListRef = ref(null)
const addressItemRefs = ref(new Map())

const skeletonCount = 3
const hasAddresses = computed(() => !isEmpty(addressList.value))

const setAddressItemRef = (el, addressId) => {
  if (el) {
    addressItemRefs.value.set(addressId, el)
  } else {
    addressItemRefs.value.delete(addressId)
  }
}

const findDefaultAddress = (list) =>
  list.find(item => item.isDefault === '1')

const loadAddressList = async () => {
  if (loading.value) return

  loading.value = true
  error.value = null
  showLoadingToast()

  try {
    await userStore.queryAddrList({ force: true })
    addressList.value = userStore.addressList || []

    const defaultAddress = findDefaultAddress(addressList.value)
    if (defaultAddress?.addressId) {
      await nextTick()
      // 延迟一下确保DOM完全渲染
      setTimeout(() => {
        scrollToAddress(defaultAddress.addressId)
      }, 100)
    }
  } catch (err) {
    console.error('获取地址列表失败:', err)
    error.value = err
    showToast(err.msg || '获取地址列表失败')
    addressList.value = []
  } finally {
    loading.value = false
    closeToast()
  }
}

const scrollToAddress = (addressId) => {
  if (!addressId || !addressListRef.value) {
    return
  }

  const selectedElement = addressItemRefs.value.get(addressId)

  if (selectedElement) {
    selectedElement.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'nearest'
    })
  }
}

const handleAddAddress = () => {
  router.push('/addr/add')
}

const handleEditAddress = (address) => {
  router.push({
    path: '/addr/edit',
    query: { addrId: address.addressId, isEdit: 1 },
    params: { address }
  })
}

const handleDeleteAddress = debounce(async (address) => {
  try {
    $alert({
      title: '删除地址',
      message: '确定要删除该地址吗？',
      showCancelButton: true,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      onConfirmCallback: async () => {
        showLoadingToast()
        try {
          const [err] = await deleteAddr(address.addressId)

          if (!err) {
            const newList = addressList.value.filter(item => item.addressId !== address.addressId)
            addressList.value = newList
            userStore.setAddrList(newList)

            if (address.isDefault === '1') {
              userStore.setDefaultAddr(null)
            }

            showToast('删除成功')
          } else {
            console.error('删除地址失败:', err)
            showToast(err.msg || '删除地址失败')
          }
        } catch (error) {
          console.error('删除地址异常:', error)
          showToast('删除地址失败')
        } finally {
          closeToast()
        }
      }
    })
  } catch (error) {
    console.error('删除地址弹窗异常:', error)
  }
}, 300)

const handleSelectAddress = debounce(async (address) => {
  if (loading.value || address.isDefault === '1') return

  showLoadingToast()

  try {
    const [err] = await updateUserDefaultAddr(address.addressId)

    if (!err) {
      const newList = addressList.value.map(item => ({
        ...item,
        isDefault: item.addressId === address.addressId ? '1' : '0'
      }))

      addressList.value = newList
      userStore.setAddrList(newList)
      userStore.setDefaultAddr({ ...address, isDefault: '1' })

      showToast('设置默认地址成功')
    } else {
      console.error('设置默认地址失败:', err)
      showToast(err.msg || '设置默认地址失败')
    }
  } catch (error) {
    console.error('设置默认地址异常:', error)
    showToast('设置默认地址失败')
  } finally {
    closeToast()
  }
}, 300)

onMounted(() => {
  loadAddressList()
})

onUnmounted(() => {

})

defineExpose({
  scrollToAddress,
  loadAddressList,
  addressList: readonly(addressList)
})
</script>

<style scoped lang="less">
.address-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F8F9FA;
  overflow: hidden;
  box-sizing: border-box;
  contain: layout style paint;

  &--has-data {
    padding-bottom: 65px;
  }

  &__container {
    flex: 1;
    padding: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &__list {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    will-change: scroll-position;
    transform: translateZ(0);
  }

  &__item {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__empty {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 100;
    will-change: transform;
  }
}

.address-skeleton {
  padding: 15px;
  background-color: #FFFFFF;
  border-radius: 4px;
  margin-bottom: 8px;
  contain: layout style paint;

  &__content {
    margin-bottom: 16px;
  }

  &__header {
    margin-bottom: 11px;
  }

  &__user-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
  will-change: background-position;
}

.skeleton-name {
  width: 60px;
  height: 16px;
}

.skeleton-phone {
  width: 100px;
  height: 16px;
}

.skeleton-address {
  width: 100%;
  height: 13px;
  margin-bottom: 6px;
}

.skeleton-address-detail {
  width: 80%;
  height: 13px;
}

.skeleton-btn {
  width: 40px;
  height: 25px;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}
</style>
