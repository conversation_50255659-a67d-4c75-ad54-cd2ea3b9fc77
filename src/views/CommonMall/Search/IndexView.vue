<template>
  <main class="search">
    <SearchHeader
      ref="searchHeaderRef"
      v-model="searchKeyword"
      placeholder="搜索商品"
      @search="handleSearch"
    />

    <section
      v-if="historyRecords.length > 0"
      class="search__history"
      aria-label="搜索历史"
    >
      <div class="search__history-header">
        <h2 class="search__history-title">搜索历史</h2>
        <button
          type="button"
          class="search__clear-btn"
          @click="handleClearAllHistory"
          aria-label="清空搜索历史"
        >
          <img
            src="../../../static/images/delete.png"
            alt=""
            class="search__clear-icon"
            loading="lazy"
            width="16"
            height="16"
          />
        </button>
      </div>

      <div class="search__history-list">
        <button
          v-for="(keyword, index) in historyRecords"
          :key="`history-${index}-${keyword}`"
          type="button"
          class="search__history-keyword"
          @click="handleUseHistoryKeyword(keyword)"
          :aria-label="`搜索 ${keyword}`"
        >
          {{ keyword }}
        </button>
      </div>
    </section>
  </main>
</template>
<script setup>
import SearchHeader from '@components/Common/SearchHeader.vue'
import { onMounted, ref, nextTick } from 'vue'
import { delHistoryRecord, getHistoryRecords } from '@api/interface/search.js'
import { useRoute, useRouter } from 'vue-router'
import { get, debounce, throttle } from 'lodash-es'
import { closeToast, showLoadingToast } from 'vant'

// 响应式数据
const route = useRoute()
const router = useRouter()
const searchKeyword = ref('')
const historyRecords = ref([])
const searchHeaderRef = ref(null)
const isLoading = ref(false)

// 获取搜索历史记录
const fetchHistoryRecords = throttle(async () => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    showLoadingToast()
    const [err, data] = await getHistoryRecords()
    closeToast()
    if (!err && data) {
      historyRecords.value = data
    }
  } catch (error) {
    // console.error('获取搜索历史失败:', error)
  } finally {
    isLoading.value = false
  }
}, 500)

// 使用历史记录中的关键词
const handleUseHistoryKeyword = throttle((keyword) => {
  if (!keyword?.trim()) return
  searchKeyword.value = keyword
  handleSearch()
}, 200)

// 删除单个历史记录
// const deleteHistoryItem = async (content) => {
//   if (!content?.trim()) return
//
//   try {
//     const [err] = await delHistoryRecord({
//       type: 'SINGLE',
//       content
//     })
//     if (!err) {
//       await fetchHistoryRecords()
//     }
//   } catch (error) {
//     console.error('删除历史记录失败:', error)
//   }
// }

// 清空所有历史记录
const handleClearAllHistory = debounce(async () => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    showLoadingToast()
    const [err] = await delHistoryRecord({
      type: 'ALL'
    })
    closeToast()
    if (!err) {
      historyRecords.value = []
    }
  } catch (error) {
    // console.error('清空历史记录失败:', error)
  } finally {
    isLoading.value = false
  }
}, 300)

// 搜索处理
const handleSearch = debounce(() => {
  const keyword = searchKeyword.value?.trim()
  if (!keyword) return

  const testDMX = get(route.query, 'testDMX', false)

  // 跳转到搜索列表页面，并携带关键词参数
  router.push({
    path: '/search/list',
    query: {
      keyword,
      testDMX
    }
  })

  // 搜索后异步刷新历史记录，不阻塞跳转
  nextTick(() => {
    fetchHistoryRecords()
  })
}, 300)

const initializeComponent = async () => {
  // 并行执行初始化任务
  await Promise.all([
    fetchHistoryRecords(),
    nextTick(() => {
      // 确保DOM渲染完成后再聚焦
      searchHeaderRef.value?.inputRef?.focus()
    })
  ])
}

onMounted(async () => {
  await initializeComponent()
})
</script>
<style scoped lang="less">
.search {
  min-height: 100vh;
  background-color: #FFFFFF;
  transform: translateZ(0);
  will-change: scroll-position;
  contain: layout style paint;
}

.search__history {
  padding: (10px);
  contain: layout style;
}

.search__history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  contain: layout;
}

.search__history-title {
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  color: #171E24;
  line-height: 1.2;
}

.search__clear-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  background: none;
  border: none;
  color: #718096;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  // 增加点击区域，提升用户体验
  min-width: 44px;
  min-height: 44px;

  &:hover {
    background-color: #F8F9FA;
  }

  &:active {
    opacity: 0.7;
  }
}

.search__clear-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
  image-rendering: -webkit-optimize-contrast;
}

.search__history-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  contain: layout;
}

.search__history-keyword {
  display: inline-flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #F8F9FA;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  color: #4A5568;
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 200px;
  min-height: 32px;
  overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;

  // 性能优化：使用transform代替其他属性变化
  &:hover {
    background-color: darken(#F8F9FA, 5%);
    color: #171E24;
  }

  &:active {
    transform: scale(0.98);
    background-color: darken(#F8F9FA, 8%);
  }
}

// 高分辨率屏幕优化
@media (-webkit-min-device-pixel-ratio: 2) {
  .search__clear-icon {
    image-rendering: -webkit-optimize-contrast;
  }
}

// 减少动画的用户偏好支持
@media (prefers-reduced-motion: reduce) {
  .search__clear-btn,
  .search__history-keyword {
    transition: none;
  }

  .search__history-keyword:active {
    transform: none;
  }
}
</style>
