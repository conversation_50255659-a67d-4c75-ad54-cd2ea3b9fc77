<template>
  <div class="search-list-page">
    <header ref="pageHeaderRef" class="page-header">
      <ProvinceFilter @confirm="handleProvinceFilterConfirm" />

      <SearchHeader v-model="searchKeyword" placeholder="搜索商品" @search="handleSearch">
        <template #right-action>
          <button v-if="bizCode !== 'zq'" class="layout-toggle" @click="toggleLayout" type="button">
            <img :src="isWaterfallLayout ? switchLayout2Img : switchLayoutImg" alt="切换布局" width="20" height="20" />
          </button>
        </template>
      </SearchHeader>

      <SortFilterBar v-if="bizCode !== 'zq'" :sort-type="sortType" :sort-order="sortOrder"
        :has-filter-conditions="hasFilterConditions" @sort-change="handleSortChange" @filter-toggle="toggleFilter" />
    </header>

    <main class="goods-content">
      <GoodsListLayout :goods-list="goodsList" :is-loading="isLoading" :loading="loading" :finished="finished"
        :is-waterfall="isWaterfallLayout" :breakpoints="breakpoints" empty-description="未搜到相关商品" @load-more="onLoad"
        @item-click="goToDetail" @add-cart="addOneCart" @update:loading="(val) => loading = val" />
    </main>

    <FloatingBubble :offset="floatingBubbleOffset" @go-to-cart="goToCart"
      :is-show-cart="bizCode !== 'zq' || (bizCode === 'zq' && roleType !== '2')" />

    <FilterPopup v-if="bizCode !== 'zq'" v-model:show="isPopupShow" v-model="filterCriteria"
      :location-text="locationText" :category-id="categoryId" :keyword="searchKeyword"
      @switch-address="setSwitchAddressPopupShow" @confirm="handleFilterConfirm" @reset="handleFilterReset" />

    <AddressSwitchPopup v-if="bizCode !== 'zq'" v-model:show="isSwitchAddressPopupShow"
      @address-changed="handleAddressChanged" />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, computed, watch, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { debounce, compact, get } from 'lodash-es'
import SearchHeader from '@components/Common/SearchHeader.vue'
import AddressSwitchPopup from '@components/Common/FilterTools/AddressSwitchPopup.vue'
import FilterPopup from '@components/Common/FilterTools/FilterPopup.vue'
import FloatingBubble from '@components/Common/FloatingBubble.vue'
import SortFilterBar from '@components/Common/FilterTools/SortFilterBar.vue'
import GoodsListLayout from '@components/GoodsListCommon/GoodsListLayout.vue'
import { useGoodsList } from '@/composables/useGoodsList.js'
import { getBizCode } from '@utils/curEnv.js'
import { searchKeyWord } from '@api/index.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import switchLayoutImg from '@/static/images/switch-layout.png'
import switchLayout2Img from '@/static/images/switch-layout2.png'
import { useUserStore } from '@store/modules/user.js'
import { getDefaultBreakpoints } from '@/config/responsive.js'
import { getCustomerManagerInfo, getEnterpriseManagerInfo } from '@utils/zqInfo.js'
// 异步加载 ProvinceFilter 组件
const ProvinceFilter = defineAsyncComponent(() => import('@components/ZQCommon/ProvinceFilter.vue'))

const bizCode = getBizCode()

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()

const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})

// 使用商品列表组合函数
const {
  goodsList,
  loading,
  finished,
  isLoading,
  pageNo,
  pageSize,
  filterCriteria,
  hasFilterConditions,
  locationText,
  addressInfo,
  resetList,
  processGoodsData,
  applyStockFilter,
  goToDetail,
  goToCart,
  addOneCart,
  handleFilterReset,
  handleAddressChanged
} = useGoodsList()

// 页面特有状态
const floatingBubbleOffset = ref({ bottom: 150 })
const searchKeyword = ref('')
const sortType = ref('sort')
const sortOrder = ref('')
const isWaterfallLayout = ref(false)
const categoryId = ref('')
const isPopupShow = ref(false)
const isSwitchAddressPopupShow = ref(false)

// 动态计算头部高度
const headerHeight = ref(0)
const pageHeaderRef = ref(null)

const dynamicPaddingTop = computed(() => {
  return headerHeight.value > 0 ? `${headerHeight.value}px` : '85px'
})

// 瀑布流配置
const breakpoints = ref(getDefaultBreakpoints())

// 页面方法
const toggleLayout = () => {
  isWaterfallLayout.value = !isWaterfallLayout.value
}

const toggleFilter = () => {
  isPopupShow.value = !isPopupShow.value
}

const setSwitchAddressPopupShow = () => {
  isSwitchAddressPopupShow.value = true
}

// 防抖搜索
const debouncedSearch = debounce(() => {
  resetList()
  fetchGoodsList()
}, 300)

const handleSearch = () => {
  router.replace({
    path: route.path,
    query: {
      ...route.query,
      keyword: searchKeyword.value
    }
  })
  debouncedSearch()
}

// 处理省分筛选确认
const handleProvinceFilterConfirm = (selection) => {
  console.log('省分筛选选择:', selection)
  // 重置列表并重新加载数据
  resetList()
  fetchGoodsList()
}

const handleSortChange = ({ type, currentSortType, currentSortOrder }) => {
  if (currentSortType === type) {
    if (type === 'price' || type === 'sale') {
      sortOrder.value = currentSortOrder === 'asc' ? 'desc' : 'asc'
    }
  } else {
    sortType.value = type
    sortOrder.value = ''
  }

  resetList()
  fetchGoodsList()
}

const handleFilterConfirm = () => {
  resetList()
  fetchGoodsList()
}

// 搜索相关状态
const consecutiveEmptyPages = ref(0)
const maxEmptyPages = 2

const getQueryProvinceCode = () => {
  const queryProvinceCode = route.query.proStr
  if (queryProvinceCode) {
    // 如果是字符串且包含逗号，则分割后再合并
    return typeof queryProvinceCode === 'string' ? queryProvinceCode : queryProvinceCode.join(',')
  }
  return ''
}

const getQuerySupplierCodeList = () => {
  const querySupplierCodeList = route.query.supplierCode
  if (querySupplierCodeList) {
    // 确保返回数组格式
    if (Array.isArray(querySupplierCodeList)) {
      return querySupplierCodeList
    } else if (typeof querySupplierCodeList === 'string') {
      // 如果是逗号分隔的字符串，则分割成数组
      return querySupplierCodeList.indexOf(',') > -1
        ? querySupplierCodeList.split(',')
        : [querySupplierCodeList]
    }
  }
  return []
}

// 获取商品列表
const fetchGoodsList = async () => {
  showLoadingToast()

  if (pageNo.value === 1) {
    await userStore.queryDefaultAddr({ force: true })
    isLoading.value = true
    goodsList.value = []
    consecutiveEmptyPages.value = 0
    finished.value = false
  }

  const brandList = compact(
    filterCriteria.value.brandsList
      .filter(item => item.isSelected)
      .map(item => item.value)
  )

  const testDMX = get(route.query, 'testDMX', 'false')

  // 构建搜索参数
  let searchParams = {
    keyword: searchKeyword.value,
    bizCode: getBizCode('GOODS'),
    pageNumber: pageNo.value,
    pageSize: pageSize.value,
    orderType: '00',
    orderRule: sortOrder.value,
    brands: brandList,
    minPrice: filterCriteria.value.minPrice !== '' ? Number(filterCriteria.value.minPrice) : '',
    maxPrice: filterCriteria.value.maxPrice !== '' ? Number(filterCriteria.value.maxPrice) : '',
    addressJsonInfo: addressInfo.value,
    testDMX
  }

  if (bizCode === 'zq') {
    const zqInfo = userStore.zqInfo || {} // 假设 zqInfo 存储在 userStore 中
    searchParams = {
      ...searchParams,
      needFilter: true,
      provinceCode: getQueryProvinceCode() || (zqInfo.provinceCode ? zqInfo.provinceCode.join(',') : ''),
      supplierCodeList: getQuerySupplierCodeList().length > 0
        ? getQuerySupplierCodeList()
        : (zqInfo.isvList ? zqInfo.isvList.map(item => item.isvId) : [])
    }
  }

  const [err, json] = await searchKeyWord(searchParams)

  closeToast()
  loading.value = false
  isLoading.value = false

  if (!err) {
    if (json && json.length > 0) {
      const processedList = processGoodsData(json)
      const filteredList = applyStockFilter(processedList)

      if (filteredList.length > 0) {
        consecutiveEmptyPages.value = 0
        goodsList.value = goodsList.value.concat(filteredList)
        pageNo.value++
      } else {
        consecutiveEmptyPages.value++
        if (consecutiveEmptyPages.value >= maxEmptyPages) {
          finished.value = true
          return
        }
        pageNo.value++
        nextTick(() => onLoad())
      }
    } else {
      consecutiveEmptyPages.value++
      if (consecutiveEmptyPages.value >= maxEmptyPages) {
        finished.value = true
        return
      }
      pageNo.value++
      nextTick(() => onLoad())
    }
  } else {
    console.error('获取商品列表失败:', err.msg)
    showToast({ message: err.msg })
    consecutiveEmptyPages.value++
    if (consecutiveEmptyPages.value >= maxEmptyPages) {
      finished.value = true
    }
  }
}

const onLoad = () => {
  if (!finished.value) {
    fetchGoodsList()
  }
}

// 计算头部高度的方法
const calculateHeaderHeight = () => {
  nextTick(() => {
    if (pageHeaderRef.value) {
      // 直接获取实际的 header 高度
      headerHeight.value = pageHeaderRef.value.offsetHeight
    } else {
      // 备用计算方式
      let totalHeight = 0

      // ProvinceFilter 组件高度（当显示时）
      if (roleType.value === '4') {
        totalHeight += 44 // ProvinceFilter 的大概高度
      }

      // SearchHeader 组件高度
      totalHeight += 44 // SearchHeader 的大概高度

      // 如果有 SortFilterBar 组件显示，则加上其高度
      if (bizCode !== 'zq') {
        totalHeight += 44 // SortFilterBar 的高度
      }

      headerHeight.value = totalHeight
    }
  })
}

// 监听bizCode变化，重新计算头部高度
watch(() => bizCode, () => {
  calculateHeaderHeight()
}, { flush: 'post' })

onMounted(async () => {
  if (route.query.keyword) {
    searchKeyword.value = route.query.keyword
  }

  await userStore.queryDefaultAddr({ force: true })

  // 计算头部高度
  calculateHeaderHeight()

  fetchGoodsList()
})
</script>

<style scoped lang="less">
.search-list-page {
  padding-top: v-bind(dynamicPaddingTop);

  .page-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    .layout-toggle {
      margin-left: 12px;
      padding: 4px;
      background: none;
      border: none;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .goods-content {
    padding: 0 10px;
  }
}
</style>
