<template>
  <section :class="containerClass">
    <div :class="contentClass">
      <img
        :src="imageUrl"
        :alt="imageAlt"
        :class="imageClass"
        loading="lazy"
      />
      <p :class="textClass">{{ text }}</p>
      <WoButton
        :type="buttonType"
        size="medium"
        :class="buttonClass"
        @click="handleButtonClick"
      >
        {{ buttonText }}
      </WoButton>
    </div>
  </section>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import { getMallName } from '@utils/curEnv.js'
import noGoodsImg from '../assets/no-goods.png'

const props = defineProps({
  type: {
    type: String,
    required: true,
    validator: (value) => ['login', 'empty'].includes(value)
  }
})

const { type } = toRefs(props)

const emit = defineEmits(['login', 'go-shopping'])

const config = computed(() => {
  if (type.value === 'login') {
    return {
      containerClass: 'cart-login',
      contentClass: 'cart-login__content',
      imageClass: 'cart-login__image',
      textClass: 'cart-login__text',
      buttonClass: 'cart-login__button',
      imageUrl: noGoodsImg,
      imageAlt: '购物车空空如也',
      text: '登录后可同步购物车中商品',
      buttonText: '立即登录',
      buttonType: 'gradient',
      event: 'login'
    }
  } else {
    return {
      containerClass: 'cart-empty',
      contentClass: 'cart-empty__content',
      imageClass: 'cart-empty__image',
      textClass: 'cart-empty__text',
      buttonClass: 'cart-empty__button',
      imageUrl: noGoodsImg,
      imageAlt: '购物车空空如也',
      text: '购物车空空如也，去逛逛吧~',
      buttonText: `去逛逛${getMallName()}`,
      buttonType: 'secondary',
      event: 'go-shopping'
    }
  }
})

const containerClass = computed(() => config.value.containerClass)
const contentClass = computed(() => config.value.contentClass)
const imageClass = computed(() => config.value.imageClass)
const textClass = computed(() => config.value.textClass)
const buttonClass = computed(() => config.value.buttonClass)
const imageUrl = computed(() => config.value.imageUrl)
const imageAlt = computed(() => config.value.imageAlt)
const text = computed(() => config.value.text)
const buttonText = computed(() => config.value.buttonText)
const buttonType = computed(() => config.value.buttonType)

const handleButtonClick = () => {
  emit(config.value.event)
}
</script>

<style scoped lang="less">
// 未登录状态样式
.cart-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  position: relative;
  contain: layout style paint;

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__image {
    width: 180px;
    height: 160px;
    margin-bottom: 24px;
    opacity: 0.9;
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
    position: relative;
    z-index: 1;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    animation: cart-float 3s ease-in-out infinite;
    will-change: transform;
  }

  &__text {
    font-size: 13px;
    color: #4A5568;
    margin-bottom: 10px;
    line-height: 1.5;
    font-weight: 400;
    position: relative;
    z-index: 1;
  }

  &__button {
    width: 200px;
    height: 44px;
    position: relative;
    z-index: 1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// 空购物车状态样式
.cart-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
  contain: layout style paint;

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  &__image {
    width: 180px;
    height: 160px;
    margin-bottom: 10px;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  &__text {
    font-size: 14px;
    color: #718096;
    margin-bottom: 10px;
    line-height: 1.5;
  }

  &__button {
    width: 200px;
  }
}

// 浮动动画
@keyframes cart-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

// 性能优化
.cart-login,
.cart-empty {
  transform: translateZ(0);
  backface-visibility: hidden;
}
</style>
