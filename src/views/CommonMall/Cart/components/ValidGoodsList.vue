<template>
  <div class="cart-goods__valid" :class="{ 'cart-goods__valid--group': isZQBiz }" v-for="(group, groupIndex) in validGoodsList" :key="groupIndex">
    <CartGroupHeader
      v-if="isZQBiz"
      :group="group"
      :is-edit-mode="isEditMode"
      :temp-selected-items="tempSelectedItems"
      @toggle-select="handleToggleGroupSelect" />
    <ValidGoodsItem v-for="(item, itemIndex) in group.goodsList" :key="item.cartSkuId" :item="item"
      :ref-key="`goodsItem_${groupIndex}_${itemIndex}`" :item-index="itemIndex" :is-edit-mode="isEditMode"
      :is-edit-selected="isEditModeItemSelected(item)" @toggle-select="handleToggleItemSelect"
      @show-stepper="showStepper" @quantity-change="handleQuantityChange" @look-similar="handleLookSimilar"
      @delete-item="handleDeleteItem" @close-menu="closeLongPressMenu" @swipe-open="handleSwipeOpen"
      @swipe-close="handleSwipeClose" @set-ref="setGoodsItemRef" @long-press="handleLongPress"
      @gift-click="handleGiftClick" @content-click="handleContentClick"
      :style="{ contentVisibility: itemIndex > 5 ? 'auto' : 'visible' }" />
  </div>
</template>

<script setup>
import CartGroupHeader from './CartGroupHeader.vue'
import ValidGoodsItem from './ValidGoodsItem.vue'
import { getBizCode } from '@utils/curEnv.js'
import { computed } from 'vue'
const props = defineProps({
  validGoodsList: {
    type: Array,
    required: true
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  tempSelectedItems: {
    type: Set,
    required: true
  }
})

const emit = defineEmits([
  'toggle-group-select',
  'toggle-item-select',
  'show-stepper',
  'quantity-change',
  'look-similar',
  'delete-item',
  'close-menu',
  'swipe-open',
  'swipe-close',
  'set-ref',
  'long-press',
  'gift-click',
  'content-click'
])

// 仅政企商城支持分组选择
const isZQBiz = computed(() => getBizCode() === 'zq')


const handleToggleGroupSelect = (group) => {
  emit('toggle-group-select', group)
}

const handleToggleItemSelect = (item) => {
  emit('toggle-item-select', item)
}

const showStepper = (item) => {
  emit('show-stepper', item)
}

const handleQuantityChange = (item) => {
  emit('quantity-change', item)
}

const handleLookSimilar = (item) => {
  emit('look-similar', item)
}

const handleDeleteItem = (item) => {
  emit('delete-item', item)
}

const closeLongPressMenu = () => {
  emit('close-menu')
}

const handleSwipeOpen = (item) => {
  emit('swipe-open', item)
}

const handleSwipeClose = (item) => {
  emit('swipe-close', item)
}

const setGoodsItemRef = (el, key) => {
  emit('set-ref', el, key)
}

const handleLongPress = (item) => {
  emit('long-press', item)
}

const handleGiftClick = (data) => {
  emit('gift-click', data)
}

const handleContentClick = (data) => {
  emit('content-click', data)
}

const isEditModeItemSelected = (item) => {
  if (!props.isEditMode) return false
  const itemId = `${item.cartGoodsId}_${item.cartSkuId}`
  return props.tempSelectedItems.has(itemId)
}
</script>

<style scoped lang="less">
.cart-goods__valid {
  margin-bottom: 10px;
  contain: layout style;
}

// 政企商城分组卡片化样式
.cart-goods__valid--group {
  background: #FFFFFF;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
}
</style>
